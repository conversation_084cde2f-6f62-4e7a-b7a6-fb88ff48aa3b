using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using PEMTestSystem.Models.Core;
using PEMTestSystem.Models.Devices;

namespace PEMTestSystem.Models.System
{
    /// <summary>
    /// 系统日志实体类
    /// </summary>
    public class SystemLog
    {
        [Key]
        public long Id { get; set; }

        /// <summary>
        /// 日志级别：1-Debug, 2-Info, 3-Warning, 4-Error, 5-Fatal
        /// </summary>
        public LogLevel Level { get; set; }

        /// <summary>
        /// 日志内容
        /// </summary>
        [Column(TypeName = "nvarchar(max)")]
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 异常详细信息
        /// </summary>
        [Column(TypeName = "nvarchar(max)")]
        public string? Exception { get; set; }

        /// <summary>
        /// 日志来源（类名、方法名等）
        /// </summary>
        [MaxLength(255)]
        public string? Source { get; set; }

        /// <summary>
        /// 日志分类：System, Device, Experiment, Safety
        /// </summary>
        [MaxLength(100)]
        public string? Category { get; set; }

        /// <summary>
        /// 关联实验ID
        /// </summary>
        public Guid? ExperimentId { get; set; }

        /// <summary>
        /// 关联设备ID
        /// </summary>
        public Guid? DeviceId { get; set; }

        /// <summary>
        /// 附加信息（JSON格式）
        /// </summary>
        [Column(TypeName = "nvarchar(max)")]
        public string? Properties { get; set; }

        public DateTime Timestamp { get; set; } = DateTime.Now;

        // 导航属性
        [ForeignKey(nameof(ExperimentId))]
        public virtual Experiment? Experiment { get; set; }

        [ForeignKey(nameof(DeviceId))]
        public virtual Device? Device { get; set; }
    }

    public enum LogLevel
    {
        Debug = 1,
        Info = 2,
        Warning = 3,
        Error = 4,
        Fatal = 5
    }
}