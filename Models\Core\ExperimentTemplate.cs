using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace PEMTestSystem.Models.Core
{
    /// <summary>
    /// 实验参数模板实体类
    /// </summary>
    public class ExperimentTemplate
    {
        [Key]
        public Guid Id { get; set; } = Guid.NewGuid();

        [Required]
        [MaxLength(255)]
        public string Name { get; set; } = string.Empty;

        [MaxLength(1000)]
        public string? Description { get; set; }

        /// <summary>
        /// 实验类型：1-恒定电流, 2-恒定电压, 3-线性提升电压
        /// </summary>
        [Required]
        public ExperimentType ExperimentType { get; set; }

        /// <summary>
        /// 参数配置（JSON格式）
        /// </summary>
        [Required]
        [Column(TypeName = "nvarchar(max)")]
        public string Configuration { get; set; } = string.Empty;

        /// <summary>
        /// 是否为默认模板
        /// </summary>
        public bool IsDefault { get; set; } = false;

        /// <summary>
        /// 是否为系统预设模板
        /// </summary>
        public bool IsSystem { get; set; } = false;

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime UpdatedAt { get; set; } = DateTime.Now;
    }
}