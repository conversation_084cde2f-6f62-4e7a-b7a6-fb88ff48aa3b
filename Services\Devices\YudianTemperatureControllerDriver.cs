using System;
using System.Threading.Tasks;
using PEMTestSystem.Models.Devices;

namespace PEMTestSystem.Services.Devices
{
    /// <summary>
    /// 宇电 MK008 温控器驱动
    /// 支持 Modbus RTU 通信协议，提供完整的温度控制和监测功能
    /// </summary>
    /// <remarks>
    /// 该驱动程序适用于宇电MK008系列温控器，通过Modbus RTU协议实现设备控制
    /// 主要功能包括：
    /// - 温度设置和监测（20-90°C，动态精度支持）
    /// - 动态小数位数检测（0-3位小数，基于LED状态寄存器）
    /// - 加热控制（开启/停止）
    /// - 加热状态监控
    /// - 报警温度设置
    /// - 实时数据读取
    /// - 设备状态管理
    /// - LED状态解析（输出状态、报警状态、温度单位等）
    ///
    /// ==================== 方法目录索引 ====================
    ///
    /// 【构造函数】
    /// - YudianTemperatureControllerDriver(string, int, byte, string, string) // public - 构造函数，初始化温控器驱动
    ///
    /// 【属性】
    /// - DeviceId, Type, DeviceName, Model                    // public    - 设备基本信息属性
    /// - LastCommunicationTime, Status, IsConnected          // public    - 设备状态属性
    /// - CurrentTemperature, TargetTemperature, IsHeating     // public    - 温控器状态属性
    ///
    /// 【事件】
    /// - StatusChanged                                        // public    - 设备状态变化事件
    /// - TemperatureChanged                                   // public    - 温度变化事件
    /// - OnStatusChanged(DeviceStatus, DeviceStatus, string?) // private   - 触发状态变化事件
    /// - OnTemperatureChanged(double, double)                 // private   - 触发温度变化事件
    ///
    /// 【IDevice接口实现】
    /// - ConnectAsync()                                       // public    - 连接到温控器设备
    /// - DisconnectAsync()                                    // public    - 断开温控器连接
    /// - InitializeAsync()                                    // public    - 初始化温控器
    /// - ResetAsync()                                         // public    - 重置温控器
    /// - GetDeviceInfoAsync()                                 // public    - 获取设备详细信息
    ///
    /// 【ITemperatureController接口实现】
    /// - SetTargetTemperatureAsync(double)                    // public    - 设置目标温度（支持动态精度）
    /// - GetCurrentTemperatureAsync()                         // public    - 获取当前实际温度（支持动态精度）
    /// - GetTargetTemperatureAsync()                          // public    - 获取目标温度（支持动态精度）
    /// - StartHeatingAsync()                                  // public    - 开始加热
    /// - StopHeatingAsync()                                   // public    - 停止加热
    /// - GetHeatingStatusAsync()                              // public    - 获取加热状态
    /// - SetAlarmTemperatureAsync(int, double)                // public    - 设置报警温度（支持动态精度）
    /// - GetLedStatusAsync()                                  // public    - 获取LED状态详细信息
    ///
    /// 【私有辅助方法】
    /// - RefreshStatusAsync()                                 // private   - 刷新设备状态（支持动态小数位数）
    /// - ParseLedStatus(ushort)                               // private   - 解析LED状态寄存器
    /// - ValidateTemperature(double)                          // private   - 验证温度范围
    /// - ConvertToTemperature(ushort)                         // private   - 将寄存器值转换为温度（动态精度）
    /// - ConvertFromTemperature(double)                       // private   - 将温度转换为寄存器值（动态精度）
    ///
    /// 【资源管理】
    /// - Dispose(bool)                                        // protected - 释放资源的具体实现
    ///
    /// ====================================================
    /// </remarks>
    public class YudianTemperatureControllerDriver : ModbusRtuBase, ITemperatureController
    {
        private const ushort PV_REGISTER = 0x0000;      // 测量值寄存器
        private const ushort SV_REGISTER = 0x0001;      // 设定值寄存器
        private const ushort LED_REGISTER = 0x0002;     // LED 状态寄存器
        private const ushort OUTB_REGISTER = 0x0003;    // 输出百分比寄存器
        private const ushort AT_ONOFF_REGISTER = 0x0004; // 自整定和开关寄存器
        private const ushort AL1_REGISTER = 0x0005;     // 报警值1寄存器
        private const ushort AL2_REGISTER = 0x0006;     // 报警值2寄存器

        // LED状态寄存器位掩码定义
        private const ushort LED_OUT1_MASK = 0x0001;    // BIT0: OUT1(主控输出1)状态
        private const ushort LED_OUT2_MASK = 0x0002;    // BIT1: OUT2(输出2)状态
        private const ushort LED_AT_MASK = 0x0004;      // BIT2: AT(自整定)状态
        private const ushort LED_AL1_MASK = 0x0008;     // BIT3: AL1(报警1)状态
        private const ushort LED_AL2_MASK = 0x0010;     // BIT4: AL2(报警2)状态
        private const ushort LED_ONOFF_MASK = 0x0020;   // BIT5: ON/OFF状态
        private const ushort LED_UNIT_MASK = 0x0040;    // BIT6: 温度单位(0=摄氏度, 1=华氏度)
        private const ushort LED_LBAT_MASK = 0x0080;    // BIT7: 加热环路LBAT报警
        private const ushort LED_DECIMAL_MASK = 0x0300; // BIT8-BIT9: 小数点位数

        private const double MIN_TEMPERATURE = 20.0;    // 最小温度
        private const double MAX_TEMPERATURE = 90.0;    // 最大温度
        private const double TEMPERATURE_ACCURACY = 0.1; // 默认温度精度

        private double _currentTemperature = 0.0;
        private double _targetTemperature = 0.0;
        private bool _isHeating = false;
        private int _currentDecimalPlaces = 1;          // 当前小数位数，默认1位

        public YudianTemperatureControllerDriver(
            string portName,
            int baudRate,
            byte slaveAddress,
            string deviceId,
            string deviceName)
            : base(portName, baudRate, slaveAddress)
        {
            DeviceId = deviceId;
            Type = DeviceType.TemperatureController;
            DeviceName = deviceName;
            Model = "宇电MK008";

            App.AlarmService.Debug("温控器驱动", $"初始化宇电温控器驱动 - 设备ID: {deviceId}");
        }

        #region IDevice 属性实现

        public string DeviceId { get; }
        public DeviceType Type { get; }
        public string DeviceName { get; }
        public string Model { get; }
        public DateTime LastCommunicationTime { get; private set; }

        private DeviceStatus _status = DeviceStatus.Disconnected;
        public DeviceStatus Status
        {
            get => _status;
            private set
            {
                if (_status != value)
                {
                    var oldStatus = _status;
                    _status = value;
                    OnStatusChanged(oldStatus, value);
                }
            }
        }

        public bool IsConnected => Status == DeviceStatus.Connected || Status == DeviceStatus.Running;

        #endregion

        #region ITemperatureController 属性实现

        public double CurrentTemperature => _currentTemperature;
        public double TargetTemperature => _targetTemperature;
        public bool IsHeating => _isHeating;

        /// <summary>
        /// 当前温度显示的小数位数
        /// </summary>
        public int CurrentDecimalPlaces => _currentDecimalPlaces;

        #endregion

        #region 事件

        public event EventHandler<DeviceStatusChangedEventArgs>? StatusChanged;
        public event EventHandler<TemperatureChangedEventArgs>? TemperatureChanged;

        private void OnStatusChanged(DeviceStatus oldStatus, DeviceStatus newStatus, string? message = null)
        {
            var args = new DeviceStatusChangedEventArgs(DeviceId, oldStatus, newStatus, message);
            StatusChanged?.Invoke(this, args);
            
            App.AlarmService.Info("设备状态", $"温控器 {DeviceName} 状态变更: {oldStatus} -> {newStatus}");
        }

        private void OnTemperatureChanged(double currentTemp, double targetTemp)
        {
            var args = new TemperatureChangedEventArgs(DeviceId, currentTemp, targetTemp);
            TemperatureChanged?.Invoke(this, args);
        }

        #endregion

        #region IDevice 方法实现

        public override async Task<bool> ConnectAsync()
        {
            try
            {
                Status = DeviceStatus.Connecting;
                App.AlarmService.Info("设备连接", $"正在连接温控器 {DeviceName}");

                var connected = await base.ConnectAsync();
                if (!connected)
                {
                    Status = DeviceStatus.Error;
                    return false;
                }

                // 测试通信 - 读取测量值
                var testResult = await ReadHoldingRegistersAsync(PV_REGISTER, 1);
                if (testResult == null)
                {
                    Status = DeviceStatus.Error;
                    App.AlarmService.Error("设备连接", $"温控器 {DeviceName} 通信测试失败");
                    return false;
                }

                Status = DeviceStatus.Connected;
                LastCommunicationTime = DateTime.Now;
                App.AlarmService.Info("设备连接", $"温控器 {DeviceName} 连接成功");

                // 读取初始状态
                await RefreshStatusAsync();

                return true;
            }
            catch (Exception ex)
            {
                Status = DeviceStatus.Error;
                App.AlarmService.Error("设备连接", $"温控器 {DeviceName} 连接异常", ex);
                return false;
            }
        }

        public override async Task<bool> DisconnectAsync()
        {
            try
            {
                var result = await base.DisconnectAsync();
                Status = DeviceStatus.Disconnected;
                return result;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("设备连接", $"温控器 {DeviceName} 断开连接异常", ex);
                return false;
            }
        }

        public async Task<bool> InitializeAsync()
        {
            try
            {
                if (!IsConnected)
                {
                    var connected = await ConnectAsync();
                    if (!connected)
                        return false;
                }

                App.AlarmService.Info("设备初始化", $"正在初始化温控器 {DeviceName}");

                // 读取当前状态
                await RefreshStatusAsync();

                App.AlarmService.Info("设备初始化", $"温控器 {DeviceName} 初始化成功");
                return true;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("设备初始化", $"温控器 {DeviceName} 初始化异常", ex);
                return false;
            }
        }

        public async Task<bool> ResetAsync()
        {
            try
            {
                App.AlarmService.Info("设备重置", $"正在重置温控器 {DeviceName}");

                // 停止加热
                await StopHeatingAsync();

                // 重置目标温度为室温
                await SetTargetTemperatureAsync(25.0);

                App.AlarmService.Info("设备重置", $"温控器 {DeviceName} 重置成功");
                return true;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("设备重置", $"温控器 {DeviceName} 重置异常", ex);
                return false;
            }
        }

        public async Task<DeviceInfo> GetDeviceInfoAsync()
        {
            try
            {
                // 读取设备状态信息
                var registers = await ReadHoldingRegistersAsync(PV_REGISTER, 5);
                if (registers == null)
                {
                    throw new InvalidOperationException("无法读取设备信息");
                }

                // 解析LED状态获取当前小数位数
                var ledStatus = registers[2];
                ParseLedStatus(ledStatus);

                LastCommunicationTime = DateTime.Now;

                return new DeviceInfo
                {
                    DeviceId = DeviceId,
                    DeviceName = DeviceName,
                    Model = Model,
                    Version = "1.0",
                    SerialNumber = $"YD{_slaveAddress:D3}",
                    Properties = new Dictionary<string, object>
                    {
                        ["CurrentTemperature"] = ConvertToTemperature(registers[0]),
                        ["TargetTemperature"] = ConvertToTemperature(registers[1]),
                        ["OutputPercentage"] = registers[3],
                        ["SlaveAddress"] = _slaveAddress,
                        ["MinTemperature"] = MIN_TEMPERATURE,
                        ["MaxTemperature"] = MAX_TEMPERATURE,
                        ["Accuracy"] = TEMPERATURE_ACCURACY,
                        ["CurrentDecimalPlaces"] = _currentDecimalPlaces,
                        ["LEDStatus"] = ledStatus,
                        ["IsHeating"] = (ledStatus & LED_ONOFF_MASK) == 0,
                        ["TemperatureUnit"] = (ledStatus & LED_UNIT_MASK) != 0 ? "华氏度" : "摄氏度"
                    }
                };
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("设备信息", $"获取温控器 {DeviceName} 信息异常", ex);
                throw;
            }
        }

        #endregion

        #region ITemperatureController 方法实现

        public async Task<bool> SetTargetTemperatureAsync(double temperature)
        {
            try
            {
                ValidateTemperature(temperature);

                // 先读取LED状态以获取当前小数位数设置
                var ledRegisters = await ReadHoldingRegistersAsync(LED_REGISTER, 1);
                if (ledRegisters != null)
                {
                    ParseLedStatus(ledRegisters[0]);
                }

                var registerValue = ConvertFromTemperature(temperature);
                var result = await WriteSingleRegisterAsync(SV_REGISTER, registerValue);

                if (result)
                {
                    _targetTemperature = temperature;
                    LastCommunicationTime = DateTime.Now;
                    App.AlarmService.Info("温度控制",
                        $"温控器 {DeviceName} 目标温度设置为 {temperature.ToString($"F{_currentDecimalPlaces}")}°C");
                    OnTemperatureChanged(_currentTemperature, _targetTemperature);
                }
                else
                {
                    App.AlarmService.Error("温度控制", $"温控器 {DeviceName} 设置目标温度失败");
                }

                return result;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("温度控制", $"温控器 {DeviceName} 设置目标温度异常", ex);
                return false;
            }
        }

        public async Task<double> GetCurrentTemperatureAsync()
        {
            try
            {
                // 同时读取PV和LED寄存器以获取准确的小数位数信息
                var registers = await ReadHoldingRegistersAsync(PV_REGISTER, 3);
                if (registers == null)
                {
                    throw new InvalidOperationException("读取当前温度失败");
                }

                // 先解析LED状态更新小数位数
                var ledStatus = registers[2];
                ParseLedStatus(ledStatus);

                // 然后转换温度值
                _currentTemperature = ConvertToTemperature(registers[0]);
                LastCommunicationTime = DateTime.Now;

                return _currentTemperature;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("温度读取", $"温控器 {DeviceName} 读取当前温度异常", ex);
                throw;
            }
        }

        public async Task<double> GetTargetTemperatureAsync()
        {
            try
            {
                // 同时读取SV和LED寄存器以获取准确的小数位数信息
                var registers = await ReadHoldingRegistersAsync(SV_REGISTER, 2);
                if (registers == null)
                {
                    throw new InvalidOperationException("读取目标温度失败");
                }

                // 先解析LED状态更新小数位数
                var ledStatus = registers[1];
                ParseLedStatus(ledStatus);

                // 然后转换温度值
                _targetTemperature = ConvertToTemperature(registers[0]);
                LastCommunicationTime = DateTime.Now;

                return _targetTemperature;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("温度读取", $"温控器 {DeviceName} 读取目标温度异常", ex);
                throw;
            }
        }

        public async Task<bool> StartHeatingAsync()
        {
            try
            {
                // 写入 0x0A 到 AT/ONOFF 寄存器开启加热
                var result = await WriteSingleRegisterAsync(AT_ONOFF_REGISTER, 0x0A);

                if (result)
                {
                    _isHeating = true;
                    LastCommunicationTime = DateTime.Now;
                    App.AlarmService.Info("加热控制", $"温控器 {DeviceName} 开始加热");
                }
                else
                {
                    App.AlarmService.Error("加热控制", $"温控器 {DeviceName} 启动加热失败");
                }

                return result;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("加热控制", $"温控器 {DeviceName} 启动加热异常", ex);
                return false;
            }
        }

        public async Task<bool> StopHeatingAsync()
        {
            try
            {
                // 写入 0x14 到 AT/ONOFF 寄存器关闭加热
                var result = await WriteSingleRegisterAsync(AT_ONOFF_REGISTER, 0x14);

                if (result)
                {
                    _isHeating = false;
                    LastCommunicationTime = DateTime.Now;
                    App.AlarmService.Info("加热控制", $"温控器 {DeviceName} 停止加热");
                }
                else
                {
                    App.AlarmService.Error("加热控制", $"温控器 {DeviceName} 停止加热失败");
                }

                return result;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("加热控制", $"温控器 {DeviceName} 停止加热异常", ex);
                return false;
            }
        }

        public async Task<bool> GetHeatingStatusAsync()
        {
            try
            {
                var registers = await ReadHoldingRegistersAsync(LED_REGISTER, 1);
                if (registers == null)
                {
                    throw new InvalidOperationException("读取加热状态失败");
                }

                // 解析LED状态并更新小数位数
                var ledStatus = registers[0];
                ParseLedStatus(ledStatus);

                // 检查 LED 状态寄存器的 BIT5 (ON/OFF 状态)
                _isHeating = (ledStatus & LED_ONOFF_MASK) == 0; // BIT5=0 表示加热运行，BIT5=1 表示关闭加热

                LastCommunicationTime = DateTime.Now;
                return _isHeating;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("状态读取", $"温控器 {DeviceName} 读取加热状态异常", ex);
                throw;
            }
        }

        public async Task<bool> SetAlarmTemperatureAsync(int alarmType, double temperature)
        {
            try
            {
                ValidateTemperature(temperature);

                if (alarmType != 1 && alarmType != 2)
                {
                    throw new ArgumentException("报警类型必须是 1 或 2", nameof(alarmType));
                }

                // 先读取LED状态以获取当前小数位数设置
                var ledRegisters = await ReadHoldingRegistersAsync(LED_REGISTER, 1);
                if (ledRegisters != null)
                {
                    ParseLedStatus(ledRegisters[0]);
                }

                var register = alarmType == 1 ? AL1_REGISTER : AL2_REGISTER;
                var registerValue = ConvertFromTemperature(temperature);
                var result = await WriteSingleRegisterAsync(register, registerValue);

                if (result)
                {
                    LastCommunicationTime = DateTime.Now;
                    App.AlarmService.Info("报警设置",
                        $"温控器 {DeviceName} 报警{alarmType}温度设置为 {temperature.ToString($"F{_currentDecimalPlaces}")}°C");
                }
                else
                {
                    App.AlarmService.Error("报警设置", $"温控器 {DeviceName} 设置报警{alarmType}温度失败");
                }

                return result;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("报警设置", $"温控器 {DeviceName} 设置报警温度异常", ex);
                return false;
            }
        }

        /// <summary>
        /// 获取LED状态的详细信息
        /// </summary>
        /// <returns>包含LED状态详细信息的字典</returns>
        public async Task<Dictionary<string, object>> GetLedStatusAsync()
        {
            try
            {
                var registers = await ReadHoldingRegistersAsync(LED_REGISTER, 1);
                if (registers == null)
                {
                    throw new InvalidOperationException("读取LED状态失败");
                }

                var ledStatus = registers[0];
                ParseLedStatus(ledStatus);

                LastCommunicationTime = DateTime.Now;

                return new Dictionary<string, object>
                {
                    ["RawValue"] = ledStatus,
                    ["OUT1Active"] = (ledStatus & LED_OUT1_MASK) != 0,
                    ["OUT2Active"] = (ledStatus & LED_OUT2_MASK) != 0,
                    ["ATActive"] = (ledStatus & LED_AT_MASK) != 0,
                    ["AL1Active"] = (ledStatus & LED_AL1_MASK) != 0,
                    ["AL2Active"] = (ledStatus & LED_AL2_MASK) != 0,
                    ["IsHeating"] = (ledStatus & LED_ONOFF_MASK) == 0,
                    ["TemperatureUnit"] = (ledStatus & LED_UNIT_MASK) != 0 ? "华氏度" : "摄氏度",
                    ["LBATAlarm"] = (ledStatus & LED_LBAT_MASK) != 0,
                    ["DecimalPlaces"] = _currentDecimalPlaces
                };
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("LED状态读取", $"温控器 {DeviceName} 读取LED状态异常", ex);
                throw;
            }
        }

        #endregion

        #region 私有辅助方法

        /// <summary>
        /// 刷新设备状态
        /// </summary>
        private async Task RefreshStatusAsync()
        {
            try
            {
                // 读取 PV, SV, LED, OUTB 寄存器
                var registers = await ReadHoldingRegistersAsync(PV_REGISTER, 4);
                if (registers != null)
                {
                    var oldCurrentTemp = _currentTemperature;
                    var oldTargetTemp = _targetTemperature;

                    // 解析LED状态并更新小数位数
                    var ledStatus = registers[2];
                    ParseLedStatus(ledStatus);

                    // 使用动态小数位数转换温度
                    _currentTemperature = ConvertToTemperature(registers[0]);
                    _targetTemperature = ConvertToTemperature(registers[1]);

                    // 检查加热状态
                    _isHeating = (ledStatus & LED_ONOFF_MASK) == 0; // BIT5=0 表示加热运行

                    LastCommunicationTime = DateTime.Now;

                    // 如果温度有变化，触发事件
                    if (Math.Abs(_currentTemperature - oldCurrentTemp) > 0.01 ||
                        Math.Abs(_targetTemperature - oldTargetTemp) > 0.01)
                    {
                        OnTemperatureChanged(_currentTemperature, _targetTemperature);
                    }
                }
            }
            catch (Exception ex)
            {
                App.AlarmService.Warning("状态刷新", $"温控器 {DeviceName} 状态刷新失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 解析LED状态寄存器，提取小数位数和其他状态信息
        /// </summary>
        /// <param name="ledStatus">LED状态寄存器值</param>
        private void ParseLedStatus(ushort ledStatus)
        {
            try
            {
                // 提取小数位数 (BIT8-BIT9)
                var decimalBits = (ledStatus & LED_DECIMAL_MASK) >> 8;
                var newDecimalPlaces = decimalBits switch
                {
                    0b00 => 0,  // 无小数点
                    0b01 => 1,  // 1位小数
                    0b10 => 2,  // 2位小数
                    0b11 => 3,  // 3位小数
                    _ => 1      // 默认1位小数
                };

                // 如果小数位数发生变化，记录日志
                if (_currentDecimalPlaces != newDecimalPlaces)
                {
                    App.AlarmService.Debug("LED状态解析",
                        $"温控器 {DeviceName} 小数位数变更: {_currentDecimalPlaces} -> {newDecimalPlaces}");
                    _currentDecimalPlaces = newDecimalPlaces;
                }

                // 解析其他状态信息（用于调试和监控）
                var out1Active = (ledStatus & LED_OUT1_MASK) != 0;
                var out2Active = (ledStatus & LED_OUT2_MASK) != 0;
                var atActive = (ledStatus & LED_AT_MASK) != 0;
                var al1Active = (ledStatus & LED_AL1_MASK) != 0;
                var al2Active = (ledStatus & LED_AL2_MASK) != 0;
                var isFahrenheit = (ledStatus & LED_UNIT_MASK) != 0;
                var lbatAlarm = (ledStatus & LED_LBAT_MASK) != 0;

                App.AlarmService.Debug("LED状态解析",
                    $"温控器 {DeviceName} LED状态: OUT1={out1Active}, OUT2={out2Active}, " +
                    $"AT={atActive}, AL1={al1Active}, AL2={al2Active}, " +
                    $"单位={(isFahrenheit ? "华氏度" : "摄氏度")}, LBAT={lbatAlarm}, 小数位={_currentDecimalPlaces}");
            }
            catch (Exception ex)
            {
                App.AlarmService.Warning("LED状态解析", $"温控器 {DeviceName} LED状态解析异常: {ex.Message}");
                // 解析失败时保持当前小数位数不变
            }
        }

        /// <summary>
        /// 验证温度范围
        /// </summary>
        private static void ValidateTemperature(double temperature)
        {
            if (temperature < MIN_TEMPERATURE || temperature > MAX_TEMPERATURE)
            {
                throw new ArgumentOutOfRangeException(nameof(temperature),
                    $"温度必须在 {MIN_TEMPERATURE}°C 到 {MAX_TEMPERATURE}°C 之间，当前值: {temperature}°C");
            }
        }

        /// <summary>
        /// 将寄存器值转换为温度（支持动态小数位数）
        /// </summary>
        private double ConvertToTemperature(ushort registerValue)
        {
            // 处理二进制补码
            short signedValue = (short)registerValue;

            // 检查溢出标志
            if (signedValue == -16666) // LLLL (下溢出)
            {
                return double.NegativeInfinity;
            }
            if (signedValue == 18888) // HHHH (上溢出)
            {
                return double.PositiveInfinity;
            }

            // 根据当前小数位数计算除数
            var divisor = Math.Pow(10, _currentDecimalPlaces);
            var temperature = signedValue / divisor;

            App.AlarmService.Debug("温度转换",
                $"温控器 {DeviceName} 寄存器值 {registerValue} -> 温度 {temperature.ToString($"F{_currentDecimalPlaces}")}°C (小数位数: {_currentDecimalPlaces})");

            return temperature;
        }

        /// <summary>
        /// 将温度转换为寄存器值（支持动态小数位数）
        /// </summary>
        private ushort ConvertFromTemperature(double temperature)
        {
            // 根据当前小数位数计算乘数
            var multiplier = Math.Pow(10, _currentDecimalPlaces);
            var value = (short)(temperature * multiplier);

            App.AlarmService.Debug("温度转换",
                $"温控器 {DeviceName} 温度 {temperature.ToString($"F{_currentDecimalPlaces}")}°C -> 寄存器值 {value} (小数位数: {_currentDecimalPlaces})");

            return (ushort)value;
        }

        #endregion

        #region IDisposable 实现

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                try
                {
                    // 停止加热
                    StopHeatingAsync().Wait(2000);
                }
                catch (Exception ex)
                {
                    App.AlarmService.Error("温控器驱动", "释放温控器资源时停止加热失败", ex);
                }
            }

            base.Dispose(disposing);
        }

        #endregion
    }
}
