using System.Configuration;

namespace PEMTestSystem.Services
{
    /// <summary>
    /// 配置服务实现
    /// </summary>
    public class ConfigurationService : IConfigurationService
    {
        public string GetStringSetting(string key, string defaultValue = "")
        {
            return ConfigurationManager.AppSettings[key] ?? defaultValue;
        }

        public bool GetBooleanSetting(string key, bool defaultValue = false)
        {
            var value = ConfigurationManager.AppSettings[key];
            return bool.TryParse(value, out var result) ? result : defaultValue;
        }

        public double GetDoubleSetting(string key, double defaultValue = 0.0)
        {
            var value = ConfigurationManager.AppSettings[key];
            return double.TryParse(value, out var result) ? result : defaultValue;
        }

        public int GetIntSetting(string key, int defaultValue = 0)
        {
            var value = ConfigurationManager.AppSettings[key];
            return int.TryParse(value, out var result) ? result : defaultValue;
        }

        public T GetSetting<T>(string key, T defaultValue = default!)
        {
            var value = ConfigurationManager.AppSettings[key];
            if (string.IsNullOrEmpty(value))
                return defaultValue;

            try
            {
                return (T)Convert.ChangeType(value, typeof(T));
            }
            catch
            {
                return defaultValue;
            }
        }

        public void SetSetting(string key, object value)
        {
            var config = ConfigurationManager.OpenExeConfiguration(ConfigurationUserLevel.None);
            config.AppSettings.Settings.Remove(key);
            config.AppSettings.Settings.Add(key, value.ToString());
            config.Save(ConfigurationSaveMode.Modified);
            ConfigurationManager.RefreshSection("appSettings");
        }
    }
}
