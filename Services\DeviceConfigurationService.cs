using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using PEMTestSystem.Data;
using PEMTestSystem.Models.Configuration;
using PEMTestSystem.Models.Core;
using PEMTestSystem.Models.Devices;
using System.IO;

namespace PEMTestSystem.Services
{
    /// <summary>
    /// 设备配置管理服务
    /// </summary>
    public class DeviceConfigurationService
    {
        private readonly PEMTestDbContext _context;
        private readonly string _configFilePath;
        private SystemDeviceConfiguration? _currentConfiguration;

        public DeviceConfigurationService(PEMTestDbContext context)
        {
            _context = context;
            _configFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Config", "DeviceSettings.json");
            
            // 确保配置目录存在
            var configDir = Path.GetDirectoryName(_configFilePath);
            if (!string.IsNullOrEmpty(configDir) && !Directory.Exists(configDir))
            {
                Directory.CreateDirectory(configDir);
            }
        }

        /// <summary>
        /// 获取当前设备配置
        /// </summary>
        public async Task<SystemDeviceConfiguration> GetConfigurationAsync()
        {
            if (_currentConfiguration == null)
            {
                _currentConfiguration = await LoadConfigurationAsync();
            }
            return _currentConfiguration;
        }

        /// <summary>
        /// 保存设备配置
        /// </summary>
        public async Task<bool> SaveConfigurationAsync(SystemDeviceConfiguration configuration)
        {
            try
            {
                // 保存到文件
                var json = JsonConvert.SerializeObject(configuration, Formatting.Indented);
                await File.WriteAllTextAsync(_configFilePath, json);

                // 更新数据库中的设备配置
                await UpdateDatabaseConfigurationAsync(configuration);

                _currentConfiguration = configuration;

                App.AlarmService.Info("配置管理", "设备配置保存成功");

                return true;
            }
            catch (Exception ex)
            {

                App.AlarmService.Error("配置管理", "保存设备配置失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 加载设备配置
        /// </summary>
        private async Task<SystemDeviceConfiguration> LoadConfigurationAsync()
        {
            try
            {
                // 首先尝试从文件加载
                if (File.Exists(_configFilePath))
                {
                    var json = await File.ReadAllTextAsync(_configFilePath);
                    var config = JsonConvert.DeserializeObject<SystemDeviceConfiguration>(json);
                    if (config != null)
                    {

                        return config;
                    }
                }

                // 如果文件不存在或加载失败，从数据库加载
                var dbConfig = await LoadFromDatabaseAsync();
                if (dbConfig != null)
                {

                    return dbConfig;
                }

                // 如果都失败，返回默认配置
                App.AlarmService.Warning("配置管理", "使用默认设备配置");
                return new SystemDeviceConfiguration();
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("配置管理", "加载设备配置失败，使用默认配置", ex);
                App.AlarmService.Warning("配置管理", "加载设备配置失败，使用默认配置");
                return new SystemDeviceConfiguration();
            }
        }

        /// <summary>
        /// 从数据库加载配置
        /// </summary>
        private async Task<SystemDeviceConfiguration?> LoadFromDatabaseAsync()
        {
            try
            {
                var devices = await _context.Devices.ToListAsync();
                if (!devices.Any())
                    return null;

                var config = new SystemDeviceConfiguration();

                foreach (var device in devices)
                {
                    var connectionInfo = JsonConvert.DeserializeObject<dynamic>(device.ConnectionString);
                    if (connectionInfo == null) continue;

                    var deviceSettings = new DeviceConnectionSettings
                    {
                        DeviceId = device.DeviceId,
                        DeviceName = device.DeviceName,
                        PortName = connectionInfo.Port?.ToString() ?? "COM1",
                        BaudRate = connectionInfo.BaudRate ?? 9600,
                        DeviceAddress = (byte)(connectionInfo.Address ?? 1),
                        IsEnabled = true
                    };

                    switch (device.DeviceType)
                    {
                        case DeviceType.TemperatureController:
                            config.TemperatureController = deviceSettings;
                            break;
                        case DeviceType.Pump1:
                            config.FlowPump1 = deviceSettings;
                            break;
                        case DeviceType.Pump2:
                            config.FlowPump2 = deviceSettings;
                            break;
                        case DeviceType.PowerSupply:
                            config.PowerSupply = deviceSettings;
                            break;
                    }
                }

                return config;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("配置管理", "从数据库加载设备配置失败", ex);
                return null;
            }
        }

        /// <summary>
        /// 更新数据库中的设备配置
        /// </summary>
        private async Task UpdateDatabaseConfigurationAsync(SystemDeviceConfiguration configuration)
        {
            try
            {
                var deviceConfigs = new[]
                {
                    (configuration.TemperatureController, DeviceType.TemperatureController),
                    (configuration.FlowPump1, DeviceType.Pump1),
                    (configuration.FlowPump2, DeviceType.Pump2),
                    (configuration.PowerSupply, DeviceType.PowerSupply)
                };

                foreach (var (deviceConfig, deviceType) in deviceConfigs)
                {
                    var device = await _context.Devices.FirstOrDefaultAsync(d => d.DeviceId == deviceConfig.DeviceId);
                    if (device != null)
                    {
                        var connectionString = JsonConvert.SerializeObject(new
                        {
                            Port = deviceConfig.PortName,
                            BaudRate = deviceConfig.BaudRate,
                            Address = deviceConfig.DeviceAddress
                        });

                        device.ConnectionString = connectionString;
                        device.DeviceName = deviceConfig.DeviceName;
                    }
                    else
                    {
                        // 创建新设备记录
                        device = new Device
                        {
                            DeviceType = deviceType,
                            DeviceId = deviceConfig.DeviceId,
                            DeviceName = deviceConfig.DeviceName,
                            Model = GetDeviceModel(deviceType),
                            ConnectionType = GetConnectionType(deviceType),
                            ConnectionString = CreateConnectionString(deviceConfig, deviceType)
                        };
                        _context.Devices.Add(device);
                    }
                }

                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("配置管理", "更新数据库设备配置失败", ex);
                throw;
            }
        }

        /// <summary>
        /// 获取设备型号
        /// </summary>
        private static string GetDeviceModel(DeviceType deviceType)
        {
            return deviceType switch
            {
                DeviceType.TemperatureController => "宇电MK008",
                DeviceType.Pump1 or DeviceType.Pump2 => "卡川DI Pump550",
                DeviceType.PowerSupply => "ITECH IT-M3900D",
                _ => "未知型号"
            };
        }

        /// <summary>
        /// 获取设备连接类型
        /// </summary>
        private static ConnectionType GetConnectionType(DeviceType deviceType)
        {
            return deviceType switch
            {
                DeviceType.PowerSupply => ConnectionType.SerialPort,
                DeviceType.TemperatureController or DeviceType.Pump1 or DeviceType.Pump2 => ConnectionType.ModbusRTU,
                _ => ConnectionType.ModbusRTU
            };
        }

        /// <summary>
        /// 创建连接字符串
        /// </summary>
        private static string CreateConnectionString(DeviceConnectionSettings deviceConfig, DeviceType deviceType)
        {
            if (deviceType == DeviceType.PowerSupply)
            {
                // 电源设备使用串口连接，不需要设备地址
                return JsonConvert.SerializeObject(new
                {
                    Port = deviceConfig.PortName,
                    BaudRate = deviceConfig.BaudRate,
                    Timeout = 3000
                });
            }
            else
            {
                // Modbus设备需要设备地址
                return JsonConvert.SerializeObject(new
                {
                    Port = deviceConfig.PortName,
                    BaudRate = deviceConfig.BaudRate,
                    Address = deviceConfig.DeviceAddress
                });
            }
        }

        /// <summary>
        /// 测试设备连接
        /// </summary>
        public async Task<(bool Success, string Message)> TestDeviceConnectionAsync(DeviceConnectionSettings deviceSettings)
        {
            try
            {
                App.AlarmService.Info("设备测试", $"开始测试设备 {deviceSettings.DeviceName} 连接");

                // 根据设备名称判断设备类型并进行相应的测试
                var success = false;
                var message = "";

                if (deviceSettings.DeviceName.Contains("电源"))
                {
                    (success, message) = await TestPowerSupplyConnectionAsync(deviceSettings);
                }
                else
                {
                    (success, message) = await TestModbusDeviceConnectionAsync(deviceSettings);
                }

                deviceSettings.LastTestTime = DateTime.Now;
                deviceSettings.LastTestResult = message;
                deviceSettings.Status = success ? DeviceConnectionStatus.Connected : DeviceConnectionStatus.Failed;

                App.AlarmService.Info("设备测试", $"设备 {deviceSettings.DeviceName} 测试完成: {message}");

                return (success, message);
            }
            catch (Exception ex)
            {
                var errorMessage = $"测试异常: {ex.Message}";
                deviceSettings.LastTestTime = DateTime.Now;
                deviceSettings.LastTestResult = errorMessage;
                deviceSettings.Status = DeviceConnectionStatus.Failed;

                App.AlarmService.Error("设备测试", $"设备 {deviceSettings.DeviceName} 测试异常", ex);
                return (false, errorMessage);
            }
        }

        /// <summary>
        /// 测试电源设备连接（SCPI协议）
        /// </summary>
        private async Task<(bool Success, string Message)> TestPowerSupplyConnectionAsync(DeviceConnectionSettings deviceSettings)
        {
            System.IO.Ports.SerialPort? serialPort = null;
            try
            {
                // 创建串口连接
                serialPort = new System.IO.Ports.SerialPort
                {
                    PortName = deviceSettings.PortName,
                    BaudRate = deviceSettings.BaudRate,
                    DataBits = deviceSettings.DataBits,
                    StopBits = deviceSettings.StopBits,
                    Parity = deviceSettings.Parity,
                    ReadTimeout = 3000,
                    WriteTimeout = 3000
                };

                // 尝试打开串口
                serialPort.Open();
                await Task.Delay(100); // 等待串口稳定

                // 清空缓冲区
                serialPort.DiscardInBuffer();
                serialPort.DiscardOutBuffer();

                // 发送设备识别命令
                serialPort.WriteLine("*IDN?");
                await Task.Delay(100);

                // 读取响应
                var response = serialPort.ReadLine().Trim();

                if (!string.IsNullOrEmpty(response))
                {
                    return (true, $"连接成功 - 设备信息: {response}");
                }
                else
                {
                    return (false, "连接失败 - 无响应");
                }
            }
            catch (Exception ex)
            {
                return (false, $"连接失败 - {ex.Message}");
            }
            finally
            {
                serialPort?.Close();
                serialPort?.Dispose();
            }
        }

        /// <summary>
        /// 测试Modbus设备连接
        /// </summary>
        private async Task<(bool Success, string Message)> TestModbusDeviceConnectionAsync(DeviceConnectionSettings deviceSettings)
        {
            try
            {
                // 简单的串口连接测试
                using var serialPort = new System.IO.Ports.SerialPort
                {
                    PortName = deviceSettings.PortName,
                    BaudRate = deviceSettings.BaudRate,
                    DataBits = deviceSettings.DataBits,
                    StopBits = deviceSettings.StopBits,
                    Parity = deviceSettings.Parity,
                    ReadTimeout = 1000,
                    WriteTimeout = 1000
                };

                serialPort.Open();
                await Task.Delay(500); // 等待连接稳定

                return (true, "串口连接成功");
            }
            catch (Exception ex)
            {
                return (false, $"连接失败 - {ex.Message}");
            }
        }

        /// <summary>
        /// 获取可用串口列表
        /// </summary>
        public string[] GetAvailableSerialPorts()
        {
            try
            {
                return System.IO.Ports.SerialPort.GetPortNames();
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("配置管理", "获取串口列表失败", ex);
                return Array.Empty<string>();
            }
        }

        /// <summary>
        /// 验证设备配置
        /// </summary>
        public (bool IsValid, List<string> Errors) ValidateConfiguration(SystemDeviceConfiguration configuration)
        {
            var errors = new List<string>();

            // 验证串口冲突
            var portGroups = new[]
            {
                configuration.TemperatureController,
                configuration.FlowPump1,
                configuration.FlowPump2,
                configuration.PowerSupply
            }.Where(d => d.IsEnabled)
             .GroupBy(d => d.PortName)
             .Where(g => g.Count() > 1);

            foreach (var group in portGroups)
            {
                var devices = string.Join(", ", group.Select(d => d.DeviceName));
                errors.Add($"串口 {group.Key} 被多个设备使用: {devices}");
            }

            // 验证地址冲突（同一串口上的设备）
            var addressGroups = new[]
            {
                configuration.TemperatureController,
                configuration.FlowPump1,
                configuration.FlowPump2,
                configuration.PowerSupply
            }.Where(d => d.IsEnabled)
             .GroupBy(d => new { d.PortName, d.DeviceAddress })
             .Where(g => g.Count() > 1);

            foreach (var group in addressGroups)
            {
                var devices = string.Join(", ", group.Select(d => d.DeviceName));
                errors.Add($"串口 {group.Key.PortName} 上地址 {group.Key.DeviceAddress} 被多个设备使用: {devices}");
            }

            return (errors.Count == 0, errors);
        }
    }
}
