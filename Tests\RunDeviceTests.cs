using System;
using System.Threading.Tasks;

namespace PEMTestSystem.Tests
{
    /// <summary>
    /// 独立的设备测试运行器
    /// </summary>
    public static class RunDeviceTests
    {
        /// <summary>
        /// 运行设备禁用/启用功能测试
        /// </summary>
        public static async Task RunTests()
        {
            Console.WriteLine("PEM电解槽自动化测试系统 - 设备禁用/启用功能测试");
            Console.WriteLine("=".PadRight(60, '='));
            Console.WriteLine();

            try
            {
                var tests = new DeviceEnableDisableTests();
                var allTestsPassed = await tests.RunAllTests();
                
                tests.Dispose();
                
                if (allTestsPassed)
                {
                    Console.WriteLine("🎉 所有测试通过！设备禁用/启用功能正常工作。");
                }
                else
                {
                    Console.WriteLine("❌ 部分测试失败，请检查实现。");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试运行异常: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }
            
            Console.WriteLine();
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
    }
}
