# 实施计划

## 前期设计和原型阶段

- [x] 1. 界面设计原型创建
  - 创建多个HTML文件分别展示不同功能界面
  - 主界面：实时数据显示、设备控制、实验状态监控
  - 实验参数设置与保存界面：参数配置、模板管理、验证提示
  - 数据导出界面：数据查询、筛选、导出功能
  - 每个界面使用独立的HTML文件（包含HTML、CSS、JavaScript）
  - 演示主要用户交互流程和界面间的导航
  - 考虑实际屏幕尺寸和使用环境
  - _需求: 需求21_

- [ ] 2. 系统设计方案文档撰写
  - 撰写包含前期沟通汇总的设计方案文档
  - 包含详细的界面设计说明和截图
  - 描述完整的实验流程
  - 作为与实验人员确认的正式材料
  - _需求: 需求22_

## 项目基础设施和核心架构

- [ ] 3. 项目基础设施搭建
  - 创建C# WPF项目结构和解决方案
  - 配置SQL Server数据库连接
  - 设置项目依赖包（OxyPlot、Entity Framework Core、Modbus.Net等）
  - 配置项目构建和调试环境
  - _需求: 需求10, 需求18_

- [ ] 4. 数据库架构实现
  - [ ] 4.1 创建数据库表结构
    - 实现Experiments、DataPoints、DeviceStatus、ExperimentTemplates、SystemLogs表
    - 创建必要的索引和外键约束
    - 添加数据库初始化脚本
    - _需求: 需求6, 需求7, 需求15_

  - [ ] 4.2 实现Entity Framework数据模型
    - 创建对应的C#实体类
    - 配置DbContext和数据库映射
    - 实现数据访问层接口和仓储模式
    - 添加数据库迁移支持
    - _需求: 需求6, 需求7_

## 核心数据模型和业务逻辑

- [ ] 5. 核心数据模型实现
  - [ ] 5.1 实验配置数据模型
    - 创建ExperimentConfig、CommonParameters等核心类
    - 实现三种实验模式的参数类（ConstantCurrentParameters、ConstantVoltageParameters、LinearVoltageRampParameters）
    - 添加JSON序列化支持
    - _需求: 需求1, 需求2, 需求3_

  - [ ] 5.2 参数验证系统
    - 创建ParameterValidator类
    - 实现各种实验模式的参数验证规则
    - 添加参数范围检查和逻辑验证（电压0-10V，电流0-170A，温度20-90°C等）
    - 实现ValidationResult类用于错误信息管理
    - _需求: 需求4, 需求5, 需求16_

## 设备通讯和控制层

- [ ] 6. 设备接口抽象层实现
  - 创建IDevice、IPowerSupply、IFlowPump、ITemperatureController接口
  - 实现设备状态管理和连接检测基类
  - 定义设备通讯协议的统一接口
  - 添加设备连接状态枚举和事件
  - _需求: 需求10, 需求11, 需求20_

- [ ] 7. 具体设备驱动实现
  - [ ] 7.1 爱德克斯IT-M3901D电源驱动
    - 实现以太网通讯协议
    - 支持电压/电流设置和读取（精度：电压0.001V，电流0.01A）
    - 实现输出控制和状态监测
    - _需求: 需求4, 需求10, 需求20_

  - [ ] 7.2 卡川DI Pump550流量泵驱动
    - 实现Modbus RTU通讯（RS-485，波特率9600，从站地址1和2）
    - 支持流量设置和读取（范围0.1-400 mL/min）
    - 实现启停控制和状态监测
    - _需求: 需求10, 需求11, 需求12, 需求20_

  - [ ] 7.3 宇电MK008温控器驱动
    - 实现Modbus RTU通讯（波特率9600，从站地址3）
    - 支持目标温度设置和当前温度读取
    - 实现加热控制和状态监测
    - _需求: 需求10, 需求11, 需求12, 需求20_

- [ ] 8. 设备管理器实现
  - 创建DeviceManager类统一管理所有设备
  - 实现设备连接/断开连接管理
  - 添加设备健康检查和自动重连机制（5秒重试，最多3次）
  - 实现设备状态事件通知
  - _需求: 需求10, 需求11, 需求20_

## 实验控制和数据采集

- [ ] 9. 实验模式控制器实现
  - [ ] 9.1 实验模式接口和基类
    - 创建IExperimentMode接口
    - 实现ExperimentModeBase基类
    - 定义实验模式生命周期方法
    - _需求: 需求1, 需求2, 需求3_

  - [ ] 9.2 具体实验模式实现
    - 实现ConstantCurrentMode恒定电流模式
    - 实现ConstantVoltageMode恒定电压模式
    - 实现LinearVoltageRampMode线性提升电压模式
    - 每种模式支持循环实验功能
    - _需求: 需求1, 需求2, 需求3, 需求8_

- [ ] 10. 实验控制器和生命周期管理
  - 创建ExperimentController实验控制器
  - 实现实验启动前置条件检查（温度±1°C，流量±5%）
  - 实现四个关键时间点记录（T1-T4）
  - 实现实验状态机和循环控制
  - 添加设备就绪等待逻辑（最长30分钟超时）
  - _需求: 需求8, 需求13, 需求14_

- [ ] 11. 多线程数据采集服务
  - [ ] 11.1 后台数据采集服务
    - 创建DataAcquisitionService类
    - 实现独立后台线程的数据采集逻辑
    - 添加线程安全的事件通知机制（使用Dispatcher）
    - 支持可配置的采样间隔（0.1-60秒）
    - _需求: 需求6, 需求17, 需求19_

  - [ ] 11.2 数据处理和存储
    - 创建DataProcessor数据处理器
    - 实现实时数据保存到数据库（500ms内完成）
    - 添加文件备份机制防止数据丢失
    - 实现数据缓冲机制确保界面响应性
    - _需求: 需求6, 需求7, 需求19_

## 用户界面实现

- [ ] 12. 主窗口界面实现
  - [ ] 12.1 主窗口布局和基础功能
    - 创建MainWindow主窗口XAML和代码
    - 实现实验模式选择区域
    - 添加参数设置和模板管理区域
    - 实现控制按钮区域（开始、停止、紧急停止）
    - 添加状态栏和进度指示
    - _需求: 需求9, 需求16_

  - [ ] 12.2 实时数据显示和图表
    - 集成OxyPlot图表控件
    - 实现三种实验模式的实时曲线显示
    - 添加数据缩放、平移等交互功能
    - 确保100ms内更新界面显示
    - _需求: 需求1, 需求2, 需求3, 需求19_

- [ ] 13. 实验参数管理界面
  - [ ] 13.1 动态参数配置面板
    - 创建ExperimentParameterPanel用户控件
    - 实现根据实验模式动态显示相关参数
    - 添加参数验证和实时错误提示
    - 支持参数精度要求（电压0.001V，电流0.01A）
    - _需求: 需求4, 需求16_

  - [ ] 13.2 参数模板管理功能
    - 创建IExperimentTemplateService接口和实现
    - 实现参数模板的保存、加载、删除功能
    - 添加默认模板管理
    - 创建TemplateSelectionDialog模板选择对话框
    - _需求: 需求16_

- [ ] 14. 设备控制和监控界面
  - 创建DeviceControlPanel设备控制面板
  - 实现设备状态实时显示（连接状态、通讯质量）
  - 添加手动参数调整功能（温度、流量）
  - 实现运行时参数调整（需求12和18）
  - 显示设备详细信息（型号、固件版本等）
  - _需求: 需求11, 需求12, 需求18, 需求20_

## 高级功能和系统优化

- [ ] 15. 数据导出功能实现
  - [ ] 15.1 数据查询和筛选服务
    - 创建数据查询服务
    - 实现时间范围选择功能
    - 添加数据筛选和排序功能
    - _需求: 需求7_

  - [ ] 15.2 多格式数据导出
    - 实现CSV格式导出
    - 实现Excel格式导出
    - 确保时间以秒为单位显示
    - 包含所有测量参数和设备状态信息
    - _需求: 需求7_

- [ ] 16. 错误处理和恢复系统
  - [ ] 16.1 错误处理框架
    - 创建IErrorHandler接口和实现
    - 实现设备错误、数据错误、系统错误的分类处理
    - 添加系统日志记录功能
    - 实现安全保护措施（1秒内停止实验，500ms紧急停止）
    - _需求: 需求5, 需求15_

  - [ ] 16.2 崩溃恢复机制
    - 创建ExperimentStateManager状态管理器
    - 实现实验状态持久化（每秒保存）
    - 添加启动时的恢复检测和用户确认对话框
    - 实现恢复流程和设备重连
    - _需求: 需求15_

- [ ] 17. 用户体验和安全优化
  - [ ] 17.1 操作确认和安全提示
    - 实现SafetyConfirmationService确认服务
    - 添加重要操作的确认对话框
    - 实现危险操作的二次确认机制
    - 添加参数输入的实时验证提示
    - _需求: 需求16_

  - [ ] 17.2 界面状态管理和响应性
    - 实现UIStateManager界面状态管理器
    - 确保长时间实验时UI保持响应
    - 优化数据更新频率和性能
    - 添加进度指示和状态反馈
    - _需求: 需求16, 需求17, 需求18_

## 系统集成和测试

- [ ] 18. 线程间通讯和系统集成
  - 集成UI线程和后台数据采集线程
  - 实现线程安全的数据传递机制
  - 添加线程异常处理和重启机制
  - 连接所有组件实现完整的实验流程
  - _需求: 需求17, 需求18_

- [ ] 19. 系统测试实现
  - [ ] 19.1 单元测试
    - 编写设备管理器测试（使用Moq模拟设备）
    - 编写实验控制器测试
    - 编写参数验证测试
    - 编写数据处理器测试
    - _需求: 所有需求_

  - [ ] 19.2 集成测试和系统测试
    - 测试完整实验流程
    - 测试长期运行稳定性
    - 测试故障恢复能力
    - 验证所有需求的实现
    - 性能测试（高频数据采集）
    - _需求: 所有需求_