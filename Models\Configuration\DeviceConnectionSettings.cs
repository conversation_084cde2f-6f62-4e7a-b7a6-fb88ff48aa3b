using System.ComponentModel.DataAnnotations;
using System.IO.Ports;

namespace PEMTestSystem.Models.Configuration
{
    /// <summary>
    /// 设备连接设置模型
    /// </summary>
    public class DeviceConnectionSettings
    {
        /// <summary>
        /// 设备ID
        /// </summary>
        [Required]
        public string DeviceId { get; set; } = string.Empty;

        /// <summary>
        /// 设备名称
        /// </summary>
        [Required]
        public string DeviceName { get; set; } = string.Empty;

        /// <summary>
        /// 串口号
        /// </summary>
        [Required]
        public string PortName { get; set; } = "COM1";

        /// <summary>
        /// 波特率
        /// </summary>
        [Range(1200, 115200)]
        public int BaudRate { get; set; } = 9600;

        /// <summary>
        /// 数据位
        /// </summary>
        [Range(7, 8)]
        public int DataBits { get; set; } = 8;

        /// <summary>
        /// 停止位
        /// </summary>
        public StopBits StopBits { get; set; } = StopBits.One;

        /// <summary>
        /// 校验位
        /// </summary>
        public Parity Parity { get; set; } = Parity.None;

        /// <summary>
        /// 设备地址（Modbus从站地址）
        /// </summary>
        [Range(1, 247)]
        public byte DeviceAddress { get; set; } = 1;

        /// <summary>
        /// 超时时间（毫秒）
        /// </summary>
        [Range(500, 10000)]
        public int TimeoutMs { get; set; } = 2000;

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 连接状态
        /// </summary>
        public DeviceConnectionStatus Status { get; set; } = DeviceConnectionStatus.Disconnected;

        /// <summary>
        /// 最后测试时间
        /// </summary>
        public DateTime? LastTestTime { get; set; }

        /// <summary>
        /// 最后测试结果
        /// </summary>
        public string? LastTestResult { get; set; }
    }

    /// <summary>
    /// 设备连接状态
    /// </summary>
    public enum DeviceConnectionStatus
    {
        /// <summary>
        /// 未连接
        /// </summary>
        Disconnected = 0,

        /// <summary>
        /// 连接中
        /// </summary>
        Connecting = 1,

        /// <summary>
        /// 已连接
        /// </summary>
        Connected = 2,

        /// <summary>
        /// 连接失败
        /// </summary>
        Failed = 3,

        /// <summary>
        /// 测试中
        /// </summary>
        Testing = 4
    }

    /// <summary>
    /// 数据采集设置
    /// </summary>
    public class DataAcquisitionSettings
    {
        /// <summary>
        /// 采样间隔（秒）
        /// </summary>
        [Range(0.1, 60.0)]
        public double SamplingIntervalSeconds { get; set; } = 1.0;

        /// <summary>
        /// 批量插入阈值（当采样间隔小于此值时使用批量插入）
        /// </summary>
        [Range(1.0, 10.0)]
        public double BatchInsertThresholdSeconds { get; set; } = 5.0;

        /// <summary>
        /// 批量插入大小
        /// </summary>
        [Range(10, 1000)]
        public int BatchSize { get; set; } = 100;

        /// <summary>
        /// 内存中最大数据点数量
        /// </summary>
        [Range(1000, 50000)]
        public int MaxDataPointsInMemory { get; set; } = 10000;

        /// <summary>
        /// 数据库写入超时时间（毫秒）
        /// </summary>
        [Range(1000, 30000)]
        public int DatabaseTimeoutMs { get; set; } = 5000;

        /// <summary>
        /// 是否启用数据缓存
        /// </summary>
        public bool EnableDataCaching { get; set; } = true;

        /// <summary>
        /// 是否启用文件备份
        /// </summary>
        public bool EnableFileBackup { get; set; } = true;

        /// <summary>
        /// 备份文件路径
        /// </summary>
        public string BackupFilePath { get; set; } = "Data\\Backup";
    }

    /// <summary>
    /// 系统设备配置
    /// </summary>
    public class SystemDeviceConfiguration
    {
        /// <summary>
        /// 温控器设置
        /// </summary>
        public DeviceConnectionSettings TemperatureController { get; set; } = new()
        {
            DeviceId = "TempController_Main",
            DeviceName = "主温控器",
            PortName = "COM4",
            BaudRate = 9600,
            DeviceAddress = 3
        };

        /// <summary>
        /// 流量泵1设置
        /// </summary>
        public DeviceConnectionSettings FlowPump1 { get; set; } = new()
        {
            DeviceId = "Pump_01",
            DeviceName = "流量泵1",
            PortName = "COM3",
            BaudRate = 9600,
            DeviceAddress = 1
        };

        /// <summary>
        /// 流量泵2设置
        /// </summary>
        public DeviceConnectionSettings FlowPump2 { get; set; } = new()
        {
            DeviceId = "Pump_02",
            DeviceName = "流量泵2",
            PortName = "COM3",
            BaudRate = 9600,
            DeviceAddress = 2
        };

        /// <summary>
        /// 电源设置（预留）
        /// </summary>
        public DeviceConnectionSettings PowerSupply { get; set; } = new()
        {
            DeviceId = "PowerSupply_Main",
            DeviceName = "可编程电源",
            PortName = "COM1",
            BaudRate = 9600,
            DeviceAddress = 1
        };

        /// <summary>
        /// 数据采集设置
        /// </summary>
        public DataAcquisitionSettings DataAcquisition { get; set; } = new();
    }
}
