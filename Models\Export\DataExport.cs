using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using PEMTestSystem.Models.Core;

namespace PEMTestSystem.Models.Export
{
    /// <summary>
    /// 数据导出记录实体类
    /// </summary>
    public class DataExport
    {
        [Key]
        public Guid Id { get; set; } = Guid.NewGuid();

        [Required]
        public Guid ExperimentId { get; set; }

        /// <summary>
        /// 导出名称
        /// </summary>
        [Required]
        [MaxLength(255)]
        public string ExportName { get; set; } = string.Empty;

        /// <summary>
        /// 导出格式：CSV, Excel, Json
        /// </summary>
        [Required]
        [MaxLength(20)]
        public string ExportFormat { get; set; } = string.Empty;

        /// <summary>
        /// 时间格式：Absolute, Relative, Both
        /// </summary>
        [Required]
        [MaxLength(20)]
        public string TimeFormat { get; set; } = string.Empty;

        /// <summary>
        /// 导出开始时间
        /// </summary>
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 导出结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// 是否包含参数变更记录
        /// </summary>
        public bool IncludeParameterChanges { get; set; } = false;

        /// <summary>
        /// 是否包含设备状态
        /// </summary>
        public bool IncludeDeviceStatus { get; set; } = false;

        /// <summary>
        /// 选中的列（逗号分隔）
        /// </summary>
        [MaxLength(1000)]
        public string? SelectedColumns { get; set; }

        /// <summary>
        /// 导出文件路径
        /// </summary>
        [MaxLength(500)]
        public string? FilePath { get; set; }

        /// <summary>
        /// 文件大小（字节）
        /// </summary>
        public long? FileSize { get; set; }

        /// <summary>
        /// 导出记录数量
        /// </summary>
        public long? RecordCount { get; set; }

        /// <summary>
        /// 导出状态：1-进行中, 2-完成, 3-失败
        /// </summary>
        public ExportStatus Status { get; set; } = ExportStatus.InProgress;

        /// <summary>
        /// 错误消息
        /// </summary>
        public string? ErrorMessage { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? CompletedAt { get; set; }

        // 导航属性
        [ForeignKey(nameof(ExperimentId))]
        public virtual Experiment Experiment { get; set; } = null!;
    }

    public enum ExportStatus
    {
        InProgress = 1,         // 进行中
        Completed = 2,          // 完成
        Failed = 3              // 失败
    }
}