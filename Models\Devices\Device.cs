using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace PEMTestSystem.Models.Devices
{
    /// <summary>
    /// 设备基本信息实体类
    /// </summary>
    public class Device
    {
        [Key]
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// 设备类型：1-电源, 2-温控器, 3-水泵1, 4-水泵2
        /// </summary>
        public DeviceType DeviceType { get; set; }

        [Required]
        [MaxLength(50)]
        public string DeviceId { get; set; } = string.Empty;

        [Required]
        [MaxLength(100)]
        public string DeviceName { get; set; } = string.Empty;

        [Required]
        [MaxLength(100)]
        public string Model { get; set; } = string.Empty;

        /// <summary>
        /// 通讯类型：1-以太网, 2-Modbus RTU
        /// </summary>
        public ConnectionType ConnectionType { get; set; }

        /// <summary>
        /// 连接字符串（IP地址或串口配置）
        /// </summary>
        [MaxLength(500)]
        public string ConnectionString { get; set; } = string.Empty;

        /// <summary>
        /// 设备规格（JSON格式）
        /// </summary>
        [Column(TypeName = "nvarchar(max)")]
        public string? Specifications { get; set; }

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 最后校准日期
        /// </summary>
        public DateTime? LastCalibrationDate { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        // 导航属性
        public virtual ICollection<PEMTestSystem.Models.Devices.DeviceStatus> DeviceStatuses { get; set; } = new List<PEMTestSystem.Models.Devices.DeviceStatus>();
    }

    public enum DeviceType
    {
        PowerSupply = 1,        // 电源
        TemperatureController = 2,// 温控器
        Pump1 = 3,              // 水泵1
        Pump2 = 4              // 水泵2
        
    }

    public enum ConnectionType
    {
        Ethernet = 1,           // 以太网
        ModbusRTU = 2,         // Modbus RTU
        SerialPort = 3         // 串口通信（如SCPI电源）
    }
}