using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using PEMTestSystem.Models.Core;

namespace PEMTestSystem.Models.Control
{
    /// <summary>
    /// 实验运行时状态实体类，用于崩溃恢复
    /// </summary>
    public class ExperimentState
    {
        [Key]
        public Guid Id { get; set; } = Guid.NewGuid();

        [Required]
        public Guid ExperimentId { get; set; }

        /// <summary>
        /// 当前状态
        /// </summary>
        public ExperimentStatus CurrentStatus { get; set; }

        /// <summary>
        /// 当前循环次数
        /// </summary>
        public int CurrentCycle { get; set; } = 0;

        /// <summary>
        /// 总循环次数
        /// </summary>
        public int TotalCycles { get; set; } = 1;

        /// <summary>
        /// 当前进度百分比
        /// </summary>
        [Column(TypeName = "decimal(5,2)")]
        public decimal CurrentProgress { get; set; } = 0;

        /// <summary>
        /// 实验配置快照
        /// </summary>
        [Column(TypeName = "nvarchar(max)")]
        public string Configuration { get; set; } = string.Empty;

        /// <summary>
        /// 设备状态快照
        /// </summary>
        [Column(TypeName = "nvarchar(max)")]
        public string? DeviceStates { get; set; }

        // 时间信息
        public DateTime? StartTime { get; set; }
        public DateTime? DeviceReadyTime { get; set; }
        public DateTime? ExperimentStartTime { get; set; }

        /// <summary>
        /// 状态保存时间
        /// </summary>
        public DateTime LastSaveTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 是否可以恢复
        /// </summary>
        public bool CanRecover { get; set; } = true;

        /// <summary>
        /// 恢复相关备注
        /// </summary>
        [MaxLength(1000)]
        public string? RecoveryNotes { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        // 导航属性
        [ForeignKey(nameof(ExperimentId))]
        public virtual Experiment Experiment { get; set; } = null!;
    }
}