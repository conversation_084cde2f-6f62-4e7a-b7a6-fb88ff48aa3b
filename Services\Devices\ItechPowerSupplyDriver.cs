using PEMTestSystem.Models.Devices;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace PEMTestSystem.Services.Devices
{
    /// <summary>
    /// ITECH IT-M3900D 可编程直流电源设备驱动程序
    /// 基于SCPI协议实现电源的完整控制功能，包括电压、电流设置、输出控制、工作模式切换等
    /// 支持恒压(CV)和恒流(CC)两种工作模式，提供实时数据监测和保护功能
    /// </summary>
    /// <remarks>
    /// 该驱动程序适用于ITECH IT-M3900D系列可编程直流电源，通过串口通信实现设备控制
    /// 主要功能包括：
    /// - 电压设置和监测（0-150V，精度0.01V）
    /// - 电流设置和监测（0-30A，精度0.001A）
    /// - 功率监测
    /// - 输出开关控制
    /// - 工作模式切换（恒压/恒流）
    /// - 保护状态管理
    /// - 设备状态监控
    ///
    /// ==================== 方法目录索引 ====================
    ///
    /// 【构造函数和初始化】
    /// - ItechPowerSupplyDriver(string, int, string, string)  // public  - 构造函数，初始化电源驱动实例
    ///
    /// 【连接和设备管理】
    /// - ConnectAsync()                                       // public  - 异步连接到电源设备
    /// - InitializeDeviceStateAsync()                         // private - 初始化设备状态，读取当前设置
    /// - ResetAsync()                                         // public  - 重置设备到出厂默认状态
    /// - GetDeviceInfoAsync()                                 // public  - 获取设备详细信息
    ///
    /// 【电压控制】
    /// - SetVoltageAsync(double)                              // public  - 设置输出电压
    /// - GetVoltageAsync()                                    // public  - 获取当前实际输出电压
    /// - GetSetVoltageAsync()                                 // public  - 获取当前设定电压值
    ///
    /// 【电流控制】
    /// - SetCurrentAsync(double)                              // public  - 设置输出电流限制
    /// - GetCurrentAsync()                                    // public  - 获取当前实际输出电流
    /// - GetSetCurrentAsync()                                 // public  - 获取当前设定电流值
    ///
    /// 【功率监测】
    /// - GetPowerAsync()                                      // public  - 获取当前实际输出功率
    ///
    /// 【输出控制】
    /// - EnableOutputAsync()                                  // public  - 开启电源输出
    /// - DisableOutputAsync()                                 // public  - 关闭电源输出
    /// - SetOutputAsync(bool)                                 // public  - 设置输出开关状态
    /// - GetOutputStatusAsync()                               // public  - 获取输出开关状态
    ///
    /// 【工作模式控制】
    /// - SetModeAsync(PowerSupplyMode)                        // public  - 设置工作模式（恒压/恒流）
    ///
    /// 【保护和错误处理】
    /// - ClearProtectionAsync()                               // public  - 清除设备保护状态
    /// - GetErrorAsync()                                      // public  - 获取设备错误信息
    ///
    /// 【辅助和验证方法】
    /// - UpdateMeasurementDataAsync()                         // private - 更新所有测量数据
    /// - ValidateVoltage(double)                              // private - 验证电压值范围
    /// - ValidateCurrent(double)                              // private - 验证电流值范围
    /// - ExtractSerialNumber(string)                          // private - 从IDN响应提取序列号
    /// - OnDataChanged()                                      // private - 触发数据变化事件
    ///
    /// ====================================================
    /// </remarks>
    public class ItechPowerSupplyDriver : ScpiSerialBase, IPowerSupply
    {
        #region SCPI命令常量定义

        /// <summary>
        /// 设置输出电压的SCPI命令
        /// </summary>
        private const string CMD_SET_VOLTAGE = "VOLT";

        /// <summary>
        /// 设置输出电流的SCPI命令
        /// </summary>
        private const string CMD_SET_CURRENT = "CURR";

        /// <summary>
        /// 设置工作模式的SCPI命令
        /// </summary>
        private const string CMD_SET_FUNCTION = "FUNC";

        /// <summary>
        /// 控制输出开关状态的SCPI命令
        /// </summary>
        private const string CMD_OUTPUT_STATE = "OUTP";

        /// <summary>
        /// 测量实际输出电压的SCPI查询命令
        /// </summary>
        private const string CMD_MEASURE_VOLTAGE = "MEAS:VOLT?";

        /// <summary>
        /// 测量实际输出电流的SCPI查询命令
        /// </summary>
        private const string CMD_MEASURE_CURRENT = "MEAS:CURR?";

        /// <summary>
        /// 测量实际输出功率的SCPI查询命令
        /// </summary>
        private const string CMD_MEASURE_POWER = "MEAS:POW?";

        /// <summary>
        /// 查询设定电压值的SCPI命令
        /// </summary>
        private const string CMD_QUERY_VOLTAGE = "VOLT?";

        /// <summary>
        /// 查询设定电流值的SCPI命令
        /// </summary>
        private const string CMD_QUERY_CURRENT = "CURR?";

        /// <summary>
        /// 查询输出开关状态的SCPI命令
        /// </summary>
        private const string CMD_QUERY_OUTPUT = "OUTP?";

        /// <summary>
        /// 清除保护状态的SCPI命令
        /// </summary>
        private const string CMD_CLEAR_PROTECTION = "OUTP:PROT:CLE";

        /// <summary>
        /// 查询系统错误信息的SCPI命令
        /// </summary>
        private const string CMD_SYSTEM_ERROR = "SYST:ERR?";

        /// <summary>
        /// 设备复位的SCPI命令
        /// </summary>
        private const string CMD_RESET = "*RST";

        #endregion

        #region 设备规格参数常量

        /// <summary>
        /// 最小输出电压值，单位：伏特(V)
        /// </summary>
        private const double MIN_VOLTAGE = 0.0;

        /// <summary>
        /// 最大输出电压值，单位：伏特(V)
        /// 根据具体设备型号可能需要调整
        /// </summary>
        private const double MAX_VOLTAGE = 150.0;

        /// <summary>
        /// 最小输出电流值，单位：安培(A)
        /// </summary>
        private const double MIN_CURRENT = 0.0;

        /// <summary>
        /// 最大输出电流值，单位：安培(A)
        /// 根据具体设备型号可能需要调整
        /// </summary>
        private const double MAX_CURRENT = 30.0;

        /// <summary>
        /// 电压设置和测量精度，单位：伏特(V)
        /// </summary>
        private const double VOLTAGE_ACCURACY = 0.01;

        /// <summary>
        /// 电流设置和测量精度，单位：安培(A)
        /// </summary>
        private const double CURRENT_ACCURACY = 0.001;

        #endregion

        #region 私有字段

        /// <summary>
        /// 当前实际输出电压值，单位：伏特(V)
        /// </summary>
        private double _currentVoltage;

        /// <summary>
        /// 当前设定电压值，单位：伏特(V)
        /// </summary>
        private double _setVoltage;

        /// <summary>
        /// 当前实际输出电流值，单位：安培(A)
        /// </summary>
        private double _currentCurrent;

        /// <summary>
        /// 当前设定电流值，单位：安培(A)
        /// </summary>
        private double _setCurrent;

        /// <summary>
        /// 当前实际输出功率值，单位：瓦特(W)
        /// </summary>
        private double _currentPower;

        /// <summary>
        /// 输出开关状态标志，true表示输出开启，false表示输出关闭
        /// </summary>
        private bool _isOutputEnabled;

        /// <summary>
        /// 当前工作模式，恒压(ConstantVoltage)或恒流(ConstantCurrent)
        /// </summary>
        private PowerSupplyMode _workingMode;

        #endregion

        #region 事件定义

        /// <summary>
        /// 电源数据变化事件，当电压、电流、功率或输出状态发生变化时触发
        /// </summary>
        /// <remarks>
        /// 该事件在以下情况下触发：
        /// - 设置电压或电流后
        /// - 开启或关闭输出后
        /// - 切换工作模式后
        /// - 定期数据更新时
        /// </remarks>
        public event EventHandler<PowerSupplyDataChangedEventArgs>? DataChanged;

        #endregion

        #region 公共属性

        /// <summary>
        /// 获取当前实际输出电压值
        /// </summary>
        /// <value>当前实际输出电压，单位：伏特(V)，范围：0-150V</value>
        /// <remarks>
        /// 该值通过SCPI命令"MEAS:VOLT?"从设备实时读取
        /// 反映设备当前的实际输出电压，可能与设定值略有差异
        /// </remarks>
        public double CurrentVoltage => _currentVoltage;

        /// <summary>
        /// 获取当前设定电压值
        /// </summary>
        /// <value>当前设定电压，单位：伏特(V)，范围：0-150V</value>
        /// <remarks>
        /// 该值为用户通过SetVoltageAsync方法设置的目标电压值
        /// 设备将尝试输出此电压值，实际输出可能受负载影响
        /// </remarks>
        public double SetVoltage => _setVoltage;

        /// <summary>
        /// 获取当前实际输出电流值
        /// </summary>
        /// <value>当前实际输出电流，单位：安培(A)，范围：0-30A</value>
        /// <remarks>
        /// 该值通过SCPI命令"MEAS:CURR?"从设备实时读取
        /// 反映设备当前的实际输出电流，受负载阻抗影响
        /// </remarks>
        public double CurrentCurrent => _currentCurrent;

        /// <summary>
        /// 获取当前设定电流值
        /// </summary>
        /// <value>当前设定电流，单位：安培(A)，范围：0-30A</value>
        /// <remarks>
        /// 该值为用户通过SetCurrentAsync方法设置的目标电流值
        /// 在恒流模式下，设备将尝试输出此电流值
        /// </remarks>
        public double SetCurrent => _setCurrent;

        /// <summary>
        /// 获取当前实际输出功率值
        /// </summary>
        /// <value>当前实际输出功率，单位：瓦特(W)，计算值：电压×电流</value>
        /// <remarks>
        /// 该值通过SCPI命令"MEAS:POW?"从设备实时读取
        /// 反映设备当前的实际输出功率，等于实际电压与实际电流的乘积
        /// </remarks>
        public double CurrentPower => _currentPower;

        /// <summary>
        /// 获取输出开关状态
        /// </summary>
        /// <value>输出状态，true表示输出开启，false表示输出关闭</value>
        /// <remarks>
        /// 该状态控制设备是否向外部负载提供电源输出
        /// 即使输出关闭，设备仍可设置电压电流参数
        /// </remarks>
        public bool IsOutputEnabled => _isOutputEnabled;

        /// <summary>
        /// 获取当前工作模式
        /// </summary>
        /// <value>工作模式，ConstantVoltage(恒压)或ConstantCurrent(恒流)</value>
        /// <remarks>
        /// 恒压模式：设备维持设定电压不变，电流随负载变化
        /// 恒流模式：设备维持设定电流不变，电压随负载变化
        /// </remarks>
        public PowerSupplyMode WorkingMode => _workingMode;

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化ITECH电源驱动程序实例
        /// </summary>
        /// <param name="portName">串口名称，如"COM1"、"COM2"等</param>
        /// <param name="baudRate">串口波特率，通常为9600、19200、38400、57600或115200</param>
        /// <param name="deviceId">设备唯一标识符，用于在系统中区分不同设备</param>
        /// <param name="deviceName">设备显示名称，用于用户界面显示和日志记录</param>
        /// <remarks>
        /// 构造函数完成以下初始化工作：
        /// - 调用基类构造函数设置串口通信参数
        /// - 设置设备类型为PowerSupply
        /// - 设置设备型号为"ITECH IT-M3900D"
        /// - 初始化工作模式为恒压模式
        /// - 记录初始化完成日志
        /// </remarks>
        /// <exception cref="ArgumentException">当串口名称无效时抛出</exception>
        /// <exception cref="ArgumentOutOfRangeException">当波特率不在支持范围内时抛出</exception>
        public ItechPowerSupplyDriver(
            string portName,
            int baudRate,
            string deviceId,
            string deviceName)
            : base(portName, baudRate, deviceId, Models.Devices.DeviceType.PowerSupply, deviceName, "ITECH IT-M3900D")
        {
            _workingMode = PowerSupplyMode.ConstantVoltage; // 默认恒压模式
            App.AlarmService.Info("设备初始化", $"ITECH电源 {deviceName} 驱动初始化完成");
        }

        #endregion

        #region 连接和初始化

        /// <summary>
        /// 异步连接到ITECH电源设备
        /// </summary>
        /// <returns>连接成功返回true，失败返回false</returns>
        /// <remarks>
        /// 连接过程包括以下步骤：
        /// 1. 设置设备状态为连接中
        /// 2. 调用基类方法建立串口连接
        /// 3. 初始化设备状态（读取当前设置和测量值）
        /// 4. 设置设备状态为已连接
        ///
        /// 连接失败时会自动设置设备状态为错误状态并记录错误日志
        /// </remarks>
        /// <exception cref="TimeoutException">连接超时时抛出</exception>
        /// <exception cref="UnauthorizedAccessException">串口被其他程序占用时抛出</exception>
        /// <exception cref="ArgumentException">串口参数无效时抛出</exception>
        public override async Task<bool> ConnectAsync()
        {
            try
            {
                Status = DeviceStatus.Connecting;
                App.AlarmService.Info("设备连接", $"正在连接ITECH电源 {DeviceName}");

                var connected = await base.ConnectAsync();
                if (!connected)
                {
                    Status = DeviceStatus.Error;
                    return false;
                }

                // 初始化设备状态
                await InitializeDeviceStateAsync();

                Status = DeviceStatus.Connected;
                App.AlarmService.Info("设备连接", $"ITECH电源 {DeviceName} 连接成功");
                return true;
            }
            catch (Exception ex)
            {
                Status = DeviceStatus.Error;
                App.AlarmService.Error("设备连接", $"ITECH电源 {DeviceName} 连接失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 异步初始化设备状态，读取设备当前的设置值和测量值
        /// </summary>
        /// <remarks>
        /// 初始化过程包括：
        /// 1. 读取当前设定电压值
        /// 2. 读取当前设定电流值
        /// 3. 读取输出开关状态
        /// 4. 更新实时测量数据（电压、电流、功率）
        ///
        /// 如果部分读取失败，会记录警告日志但不会中断初始化过程
        /// 这确保即使某些参数读取失败，设备仍可基本工作
        /// </remarks>
        /// <exception cref="InvalidOperationException">设备未连接时调用此方法</exception>
        /// <exception cref="TimeoutException">设备响应超时</exception>
        private async Task InitializeDeviceStateAsync()
        {
            try
            {
                // 读取当前设置值
                _setVoltage = await GetSetVoltageAsync();
                _setCurrent = await GetSetCurrentAsync();
                _isOutputEnabled = await GetOutputStatusAsync();

                // 读取实时测量值
                await UpdateMeasurementDataAsync();

                App.AlarmService.Info("设备初始化",
                    $"ITECH电源 {DeviceName} 状态初始化完成 - 设定电压: {_setVoltage:F2}V, 设定电流: {_setCurrent:F3}A, 输出: {(_isOutputEnabled ? "开启" : "关闭")}");
            }
            catch (Exception ex)
            {
                App.AlarmService.Warning("设备初始化", $"ITECH电源 {DeviceName} 状态初始化部分失败: {ex.Message}");
            }
        }

        #endregion

        #region 电压控制

        /// <summary>
        /// 异步设置电源输出电压
        /// </summary>
        /// <param name="voltage">目标电压值，单位：伏特(V)，有效范围：0-150V</param>
        /// <returns>设置成功返回true，失败返回false</returns>
        /// <remarks>
        /// 设置过程包括：
        /// 1. 验证电压值是否在有效范围内
        /// 2. 发送SCPI命令"VOLT {voltage}"到设备
        /// 3. 更新内部设定电压值
        /// 4. 刷新测量数据并触发数据变化事件
        ///
        /// 电压精度为0.01V，设备会自动将输入值舍入到最接近的有效值
        /// 在恒压模式下，设备将维持此电压输出
        /// </remarks>
        /// <exception cref="ArgumentOutOfRangeException">电压值超出有效范围时抛出</exception>
        /// <exception cref="InvalidOperationException">设备未连接时抛出</exception>
        /// <exception cref="TimeoutException">设备响应超时时抛出</exception>
        public async Task<bool> SetVoltageAsync(double voltage)
        {
            try
            {
                if (!ValidateVoltage(voltage))
                {
                    return false;
                    // 加入警告功能，告知设置值超出范围 todo
                }

                var command = $"{CMD_SET_VOLTAGE} {voltage:F3}";
                var success = await SendCommandAsync(command);

                if (success)
                {
                    _setVoltage = voltage;
                    App.AlarmService.Info("电源控制", $"ITECH电源 {DeviceName} 设置电压: {voltage:F3}V");

                    // 触发数据变化事件
                    await UpdateMeasurementDataAsync();
                    OnDataChanged();
                }
                else
                {
                    App.AlarmService.Error("电源控制", $"ITECH电源 {DeviceName} 设置电压失败: {voltage:F3}V");
                }

                return success;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("电源控制", $"ITECH电源 {DeviceName} 设置电压异常: {voltage:F2}V", ex);
                return false;
            }
        }

        /// <summary>
        /// 异步获取电源当前实际输出电压
        /// </summary>
        /// <returns>当前实际输出电压值，单位：伏特(V)，失败时返回0.0</returns>
        /// <remarks>
        /// 通过SCPI命令"MEAS:VOLT?"从设备读取实时电压测量值
        /// 该值反映设备当前的实际输出电压，可能与设定值略有差异
        /// 测量精度为0.01V，更新频率取决于设备内部采样率
        ///
        /// 读取成功后会自动更新内部_currentVoltage字段
        /// </remarks>
        /// <exception cref="InvalidOperationException">设备未连接时抛出</exception>
        /// <exception cref="TimeoutException">设备响应超时时抛出</exception>
        public async Task<double> GetVoltageAsync()
        {
            try
            {
                var response = await SendQueryAsync(CMD_MEASURE_VOLTAGE);
                var voltage = ParseNumericResponse(response);

                if (voltage.HasValue)
                {
                    _currentVoltage = voltage.Value;
                    return voltage.Value;
                }
                else
                {
                    App.AlarmService.Error("电源监测", $"ITECH电源 {DeviceName} 电压响应解析失败");
                    return 0.0;
                }
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("电源监测", $"ITECH电源 {DeviceName} 读取电压失败", ex);
                return 0.0;
            }
        }

        /// <summary>
        /// 异步获取电源当前设定电压值
        /// </summary>
        /// <returns>当前设定电压值，单位：伏特(V)，失败时返回0.0</returns>
        /// <remarks>
        /// 通过SCPI命令"VOLT?"从设备读取当前的电压设定值
        /// 该值为用户最后一次通过SetVoltageAsync方法设置的目标电压
        /// 与GetVoltageAsync不同，此方法返回的是设定值而非实际测量值
        ///
        /// 在设备重启或重置后，可用此方法恢复设备的设定状态
        /// </remarks>
        /// <exception cref="InvalidOperationException">设备未连接时抛出</exception>
        /// <exception cref="TimeoutException">设备响应超时时抛出</exception>
        public async Task<double> GetSetVoltageAsync()
        {
            try
            {
                var response = await SendQueryAsync(CMD_QUERY_VOLTAGE);
                var voltage = ParseNumericResponse(response);

                if (voltage.HasValue)
                {
                    return voltage.Value;
                }
                else
                {
                    App.AlarmService.Error("电源监测", $"ITECH电源 {DeviceName} 设定电压响应解析失败");
                    return 0.0;
                }
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("电源监测", $"ITECH电源 {DeviceName} 读取设定电压失败", ex);
                return 0.0;
            }
        }

        #endregion

        #region 电流控制

        /// <summary>
        /// 异步设置电源输出电流限制
        /// </summary>
        /// <param name="current">目标电流值，单位：安培(A)，有效范围：0-30A</param>
        /// <returns>设置成功返回true，失败返回false</returns>
        /// <remarks>
        /// 设置过程包括：
        /// 1. 验证电流值是否在有效范围内
        /// 2. 发送SCPI命令"CURR {current}"到设备
        /// 3. 更新内部设定电流值
        /// 4. 刷新测量数据并触发数据变化事件
        ///
        /// 电流精度为0.001A，设备会自动将输入值舍入到最接近的有效值
        /// 在恒流模式下，设备将维持此电流输出
        /// 在恒压模式下，此值作为电流限制，防止过流
        /// </remarks>
        /// <exception cref="ArgumentOutOfRangeException">电流值超出有效范围时抛出</exception>
        /// <exception cref="InvalidOperationException">设备未连接时抛出</exception>
        /// <exception cref="TimeoutException">设备响应超时时抛出</exception>
        public async Task<bool> SetCurrentAsync(double current)
        {
            try
            {
                if (!ValidateCurrent(current))
                {
                    return false;
                }

                var command = $"{CMD_SET_CURRENT} {current:F3}";
                var success = await SendCommandAsync(command);

                if (success)
                {
                    _setCurrent = current;
                    App.AlarmService.Info("电源控制", $"ITECH电源 {DeviceName} 设置电流: {current:F3}A");

                    // 触发数据变化事件
                    await UpdateMeasurementDataAsync();
                    OnDataChanged();
                }
                else
                {
                    App.AlarmService.Error("电源控制", $"ITECH电源 {DeviceName} 设置电流失败: {current:F3}A");
                }

                return success;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("电源控制", $"ITECH电源 {DeviceName} 设置电流异常: {current:F3}A", ex);
                return false;
            }
        }

        /// <summary>
        /// 异步获取电源当前实际输出电流
        /// </summary>
        /// <returns>当前实际输出电流值，单位：安培(A)，失败时返回0.0</returns>
        /// <remarks>
        /// 通过SCPI命令"MEAS:CURR?"从设备读取实时电流测量值
        /// 该值反映设备当前的实际输出电流，受负载阻抗影响
        /// 测量精度为0.001A，更新频率取决于设备内部采样率
        ///
        /// 在恒压模式下，电流值由负载决定
        /// 在恒流模式下，电流值应接近设定值
        /// 读取成功后会自动更新内部_currentCurrent字段
        /// </remarks>
        /// <exception cref="InvalidOperationException">设备未连接时抛出</exception>
        /// <exception cref="TimeoutException">设备响应超时时抛出</exception>
        public async Task<double> GetCurrentAsync()
        {
            try
            {
                var response = await SendQueryAsync(CMD_MEASURE_CURRENT);
                var current = ParseNumericResponse(response);

                if (current.HasValue)
                {
                    _currentCurrent = current.Value;
                    return current.Value;
                }
                else
                {
                    App.AlarmService.Error("电源监测", $"ITECH电源 {DeviceName} 电流响应解析失败");
                    return 0.0;
                }
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("电源监测", $"ITECH电源 {DeviceName} 读取电流失败", ex);
                return 0.0;
            }
        }

        /// <summary>
        /// 异步获取电源当前设定电流值
        /// </summary>
        /// <returns>当前设定电流值，单位：安培(A)，失败时返回0.0</returns>
        /// <remarks>
        /// 通过SCPI命令"CURR?"从设备读取当前的电流设定值
        /// 该值为用户最后一次通过SetCurrentAsync方法设置的目标电流
        /// 与GetCurrentAsync不同，此方法返回的是设定值而非实际测量值
        ///
        /// 在恒流模式下，此值为目标输出电流
        /// 在恒压模式下，此值为电流限制值
        /// 在设备重启或重置后，可用此方法恢复设备的设定状态
        /// </remarks>
        /// <exception cref="InvalidOperationException">设备未连接时抛出</exception>
        /// <exception cref="TimeoutException">设备响应超时时抛出</exception>
        public async Task<double> GetSetCurrentAsync()
        {
            try
            {
                var response = await SendQueryAsync(CMD_QUERY_CURRENT);
                var current = ParseNumericResponse(response);

                if (current.HasValue)
                {
                    return current.Value;
                }
                else
                {
                    App.AlarmService.Error("电源监测", $"ITECH电源 {DeviceName} 设定电流响应解析失败");
                    return 0.0;
                }
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("电源监测", $"ITECH电源 {DeviceName} 读取设定电流失败", ex);
                return 0.0;
            }
        }

        #endregion

        #region 功率监测

        /// <summary>
        /// 异步获取电源当前实际输出功率
        /// </summary>
        /// <returns>当前实际输出功率值，单位：瓦特(W)，失败时返回0.0</returns>
        /// <remarks>
        /// 通过SCPI命令"MEAS:POW?"从设备读取实时功率测量值
        /// 功率值等于实际输出电压与实际输出电流的乘积 (P = V × I)
        /// 该值反映设备当前向负载提供的实际功率
        ///
        /// 功率测量精度取决于电压和电流的测量精度
        /// 读取成功后会自动更新内部_currentPower字段
        /// 可用于监控设备工作状态和负载功耗
        /// </remarks>
        /// <exception cref="InvalidOperationException">设备未连接时抛出</exception>
        /// <exception cref="TimeoutException">设备响应超时时抛出</exception>
        public async Task<double> GetPowerAsync()
        {
            try
            {
                var response = await SendQueryAsync(CMD_MEASURE_POWER);
                var power = ParseNumericResponse(response);

                if (power.HasValue)
                {
                    _currentPower = power.Value;
                    return power.Value;
                }
                else
                {
                    App.AlarmService.Error("电源监测", $"ITECH电源 {DeviceName} 功率响应解析失败");
                    return 0.0;
                }
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("电源监测", $"ITECH电源 {DeviceName} 读取功率失败", ex);
                return 0.0;
            }
        }

        #endregion

        #region 输出控制

        /// <summary>
        /// 异步开启电源输出
        /// </summary>
        /// <returns>开启成功返回true，失败返回false</returns>
        /// <remarks>
        /// 这是SetOutputAsync(true)的便捷方法
        /// 开启输出后，设备将按照当前设定的电压和电流参数向负载提供电源
        /// 开启前请确保已正确设置电压和电流参数，并确认负载连接正确
        /// </remarks>
        /// <exception cref="InvalidOperationException">设备未连接时抛出</exception>
        /// <exception cref="TimeoutException">设备响应超时时抛出</exception>
        public async Task<bool> EnableOutputAsync()
        {
            return await SetOutputAsync(true);
        }

        /// <summary>
        /// 异步关闭电源输出
        /// </summary>
        /// <returns>关闭成功返回true，失败返回false</returns>
        /// <remarks>
        /// 这是SetOutputAsync(false)的便捷方法
        /// 关闭输出后，设备停止向负载提供电源，但设定参数保持不变
        /// 这是安全操作，可在调整参数或更换负载时使用
        /// </remarks>
        /// <exception cref="InvalidOperationException">设备未连接时抛出</exception>
        /// <exception cref="TimeoutException">设备响应超时时抛出</exception>
        public async Task<bool> DisableOutputAsync()
        {
            return await SetOutputAsync(false);
        }

        /// <summary>
        /// 异步设置电源输出开关状态
        /// </summary>
        /// <param name="enabled">true表示开启输出，false表示关闭输出</param>
        /// <returns>设置成功返回true，失败返回false</returns>
        /// <remarks>
        /// 通过SCPI命令"OUTP ON"或"OUTP OFF"控制设备输出开关
        /// 设置过程包括：
        /// 1. 发送输出控制命令到设备
        /// 2. 更新内部输出状态标志
        /// 3. 刷新测量数据并触发数据变化事件
        ///
        /// 开启输出前，建议先设置合适的电压和电流参数
        /// 关闭输出是安全操作，不会影响设定参数
        /// </remarks>
        /// <exception cref="InvalidOperationException">设备未连接时抛出</exception>
        /// <exception cref="TimeoutException">设备响应超时时抛出</exception>
        public async Task<bool> SetOutputAsync(bool enabled)
        {
            try
            {
                var command = $"{CMD_OUTPUT_STATE} {(enabled ? "ON" : "OFF")}";
                var success = await SendCommandAsync(command);

                if (success)
                {
                    _isOutputEnabled = enabled;
                    App.AlarmService.Info("电源控制", $"ITECH电源 {DeviceName} 输出{(enabled ? "开启" : "关闭")}");

                    // 触发数据变化事件
                    await UpdateMeasurementDataAsync();
                    OnDataChanged();
                }
                else
                {
                    App.AlarmService.Error("电源控制", $"ITECH电源 {DeviceName} 输出控制失败");
                }

                return success;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("电源控制", $"ITECH电源 {DeviceName} 输出控制异常", ex);
                return false;
            }
        }

        /// <summary>
        /// 异步获取电源当前输出开关状态
        /// </summary>
        /// <returns>输出开启返回true，输出关闭返回false，查询失败返回false</returns>
        /// <remarks>
        /// 通过SCPI命令"OUTP?"从设备读取当前的输出开关状态
        /// 该状态表示设备是否正在向外部负载提供电源输出
        ///
        /// 返回值说明：
        /// - true: 输出开启，设备正在提供电源输出
        /// - false: 输出关闭或查询失败
        ///
        /// 建议定期查询此状态以监控设备工作状态
        /// </remarks>
        /// <exception cref="InvalidOperationException">设备未连接时抛出</exception>
        /// <exception cref="TimeoutException">设备响应超时时抛出</exception>
        public async Task<bool> GetOutputStatusAsync()
        {
            try
            {
                var response = await SendQueryAsync(CMD_QUERY_OUTPUT);
                var status = ParseBooleanResponse(response);

                if (status.HasValue)
                {
                    return status.Value;
                }
                else
                {
                    App.AlarmService.Error("电源监测", $"ITECH电源 {DeviceName} 输出状态响应解析失败");
                    return false;
                }
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("电源监测", $"ITECH电源 {DeviceName} 读取输出状态失败", ex);
                return false;
            }
        }

        #endregion

        #region 工作模式控制

        /// <summary>
        /// 异步设置电源工作模式
        /// </summary>
        /// <param name="mode">工作模式，ConstantVoltage(恒压)或ConstantCurrent(恒流)</param>
        /// <returns>设置成功返回true，失败返回false</returns>
        /// <remarks>
        /// 通过SCPI命令"FUNC VOLT"或"FUNC CURR"设置设备工作模式
        ///
        /// 工作模式说明：
        /// - 恒压模式(ConstantVoltage): 设备维持设定电压不变，输出电流由负载决定，受电流限制约束
        /// - 恒流模式(ConstantCurrent): 设备维持设定电流不变，输出电压由负载决定，受电压限制约束
        ///
        /// 模式切换会触发数据变化事件，但不会影响当前的电压电流设定值
        /// 建议在输出关闭状态下切换工作模式以避免负载冲击
        /// </remarks>
        /// <exception cref="ArgumentException">工作模式参数无效时抛出</exception>
        /// <exception cref="InvalidOperationException">设备未连接时抛出</exception>
        /// <exception cref="TimeoutException">设备响应超时时抛出</exception>
        public async Task<bool> SetModeAsync(PowerSupplyMode mode)
        {
            try
            {
                var modeCommand = mode == PowerSupplyMode.ConstantVoltage ? "VOLT" : "CURR";
                var command = $"{CMD_SET_FUNCTION} {modeCommand}";
                var success = await SendCommandAsync(command);

                if (success)
                {
                    _workingMode = mode;
                    App.AlarmService.Info("电源控制", $"ITECH电源 {DeviceName} 设置工作模式: {(mode == PowerSupplyMode.ConstantVoltage ? "恒压" : "恒流")}");
                    OnDataChanged();
                }
                else
                {
                    App.AlarmService.Error("电源控制", $"ITECH电源 {DeviceName} 设置工作模式失败");
                }

                return success;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("电源控制", $"ITECH电源 {DeviceName} 设置工作模式异常", ex);
                return false;
            }
        }

        #endregion

        #region 保护和错误处理

        /// <summary>
        /// 异步清除设备保护状态
        /// </summary>
        /// <returns>清除成功返回true，失败返回false</returns>
        /// <remarks>
        /// 通过SCPI命令"OUTP:PROT:CLE"清除设备的保护状态
        ///
        /// 保护状态可能由以下原因触发：
        /// - 过压保护(OVP): 输出电压超过安全限制
        /// - 过流保护(OCP): 输出电流超过安全限制
        /// - 过功率保护(OPP): 输出功率超过设备额定值
        /// - 过温保护(OTP): 设备内部温度过高
        /// - 短路保护: 检测到输出短路
        ///
        /// 清除保护前应先排除引起保护的原因，否则保护可能再次触发
        /// 建议在清除保护后检查设备错误信息以确认保护原因
        /// </remarks>
        /// <exception cref="InvalidOperationException">设备未连接时抛出</exception>
        /// <exception cref="TimeoutException">设备响应超时时抛出</exception>
        public async Task<bool> ClearProtectionAsync()
        {
            try
            {
                var success = await SendCommandAsync(CMD_CLEAR_PROTECTION);
                if (success)
                {
                    App.AlarmService.Info("电源控制", $"ITECH电源 {DeviceName} 清除保护状态");
                }
                else
                {
                    App.AlarmService.Error("电源控制", $"ITECH电源 {DeviceName} 清除保护状态失败");
                }
                return success;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("电源控制", $"ITECH电源 {DeviceName} 清除保护状态异常", ex);
                return false;
            }
        }

        /// <summary>
        /// 异步获取设备错误信息
        /// </summary>
        /// <returns>如果存在错误返回错误描述字符串，无错误返回null</returns>
        /// <remarks>
        /// 通过SCPI命令"SYST:ERR?"查询设备的错误队列
        /// 设备会维护一个错误队列，记录运行过程中发生的各种错误
        ///
        /// 常见错误类型：
        /// - 命令语法错误: SCPI命令格式不正确
        /// - 参数超出范围: 设置的电压电流值超出设备规格
        /// - 硬件故障: 设备内部硬件异常
        /// - 通信错误: 串口通信异常
        ///
        /// 建议定期调用此方法检查设备状态，及时发现和处理错误
        /// 错误信息会自动记录到警告日志中
        /// </remarks>
        /// <exception cref="InvalidOperationException">设备未连接时抛出</exception>
        /// <exception cref="TimeoutException">设备响应超时时抛出</exception>
        public async Task<string?> GetErrorAsync()
        {
            try
            {
                var response = await SendQueryAsync(CMD_SYSTEM_ERROR);
                if (!string.IsNullOrEmpty(response) && !response.Contains("No error"))
                {
                    App.AlarmService.Warning("电源状态", $"ITECH电源 {DeviceName} 检测到错误: {response}");
                    return response;
                }
                return null;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("电源监测", $"ITECH电源 {DeviceName} 查询错误信息失败", ex);
                return null;
            }
        }

        #endregion

        #region 设备信息和重置

        /// <summary>
        /// 异步重置设备到出厂默认状态
        /// </summary>
        /// <returns>重置成功返回true，失败返回false</returns>
        /// <remarks>
        /// 通过SCPI命令"*RST"将设备重置到出厂默认状态
        ///
        /// 重置过程包括：
        /// 1. 发送重置命令到设备
        /// 2. 等待2秒让设备完成重置过程
        /// 3. 重新初始化设备状态（读取默认设置）
        ///
        /// 重置后的默认状态：
        /// - 输出关闭
        /// - 工作模式恢复为恒压模式
        /// - 电压电流设置恢复为默认值
        /// - 清除所有保护状态和错误信息
        ///
        /// 重置是安全操作，但会丢失当前所有设置，请谨慎使用
        /// </remarks>
        /// <exception cref="InvalidOperationException">设备未连接时抛出</exception>
        /// <exception cref="TimeoutException">设备响应超时时抛出</exception>
        public override async Task<bool> ResetAsync()
        {
            try
            {
                App.AlarmService.Info("电源控制", $"正在重置ITECH电源 {DeviceName}");

                var success = await SendCommandAsync(CMD_RESET);
                if (success)
                {
                    // 等待设备重置完成
                    await Task.Delay(2000);

                    // 重新初始化设备状态
                    await InitializeDeviceStateAsync();

                    App.AlarmService.Info("电源控制", $"ITECH电源 {DeviceName} 重置完成");
                }
                else
                {
                    App.AlarmService.Error("电源控制", $"ITECH电源 {DeviceName} 重置失败");
                }

                return success;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("电源控制", $"ITECH电源 {DeviceName} 重置异常", ex);
                return false;
            }
        }

        /// <summary>
        /// 异步获取设备详细信息
        /// </summary>
        /// <returns>包含设备完整信息的DeviceInfo对象</returns>
        /// <remarks>
        /// 获取设备信息的过程包括：
        /// 1. 通过"*IDN?"命令获取设备识别信息
        /// 2. 更新所有测量数据（电压、电流、功率、输出状态）
        /// 3. 更新最后通信时间
        /// 4. 构建包含所有设备信息的DeviceInfo对象
        ///
        /// 返回的DeviceInfo包含以下信息：
        /// - 基本信息：设备ID、名称、型号、版本、序列号
        /// - 实时数据：当前电压、电流、功率、输出状态、工作模式
        /// - 设备规格：电压电流范围、测量精度
        /// - 设备识别：完整的IDN响应字符串
        ///
        /// 此方法常用于设备状态监控和系统诊断
        /// </remarks>
        /// <exception cref="InvalidOperationException">设备未连接时抛出</exception>
        /// <exception cref="TimeoutException">设备响应超时时抛出</exception>
        /// <exception cref="Exception">获取设备信息失败时抛出，包含具体错误信息</exception>
        public override async Task<DeviceInfo> GetDeviceInfoAsync()
        {
            try
            {
                // 获取设备识别信息
                var idnResponse = await SendQueryAsync("*IDN?");

                // 更新测量数据
                await UpdateMeasurementDataAsync();

                LastCommunicationTime = DateTime.Now;

                return new DeviceInfo
                {
                    DeviceId = DeviceId,
                    DeviceName = DeviceName,
                    Model = Model,
                    Version = "1.0",
                    SerialNumber = ExtractSerialNumber(idnResponse),
                    Properties = new Dictionary<string, object>
                    {
                        ["Identification"] = idnResponse ?? "Unknown",
                        ["CurrentVoltage"] = _currentVoltage,
                        ["SetVoltage"] = _setVoltage,
                        ["CurrentCurrent"] = _currentCurrent,
                        ["SetCurrent"] = _setCurrent,
                        ["CurrentPower"] = _currentPower,
                        ["IsOutputEnabled"] = _isOutputEnabled,
                        ["WorkingMode"] = _workingMode.ToString(),
                        ["MinVoltage"] = MIN_VOLTAGE,
                        ["MaxVoltage"] = MAX_VOLTAGE,
                        ["MinCurrent"] = MIN_CURRENT,
                        ["MaxCurrent"] = MAX_CURRENT,
                        ["VoltageAccuracy"] = VOLTAGE_ACCURACY,
                        ["CurrentAccuracy"] = CURRENT_ACCURACY
                    }
                };
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("设备信息", $"ITECH电源 {DeviceName} 获取设备信息失败", ex);
                throw;
            }
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 异步更新所有测量数据
        /// </summary>
        /// <remarks>
        /// 批量更新设备的所有实时测量数据，包括：
        /// - 当前输出电压
        /// - 当前输出电流
        /// - 当前输出功率
        /// - 输出开关状态
        ///
        /// 此方法在以下情况下被调用：
        /// - 设备初始化时
        /// - 设置电压电流后
        /// - 开关输出后
        /// - 获取设备信息时
        ///
        /// 如果部分数据读取失败，会记录警告日志但不会中断整个更新过程
        /// 这确保即使某些测量值读取失败，其他数据仍可正常更新
        /// </remarks>
        /// <exception cref="InvalidOperationException">设备未连接时可能抛出</exception>
        private async Task UpdateMeasurementDataAsync()
        {
            try
            {
                _currentVoltage = await GetVoltageAsync();
                _currentCurrent = await GetCurrentAsync();
                _currentPower = await GetPowerAsync();
                _isOutputEnabled = await GetOutputStatusAsync();
            }
            catch (Exception ex)
            {
                App.AlarmService.Warning("电源监测", $"ITECH电源 {DeviceName} 更新测量数据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 验证电压值是否在有效范围内
        /// </summary>
        /// <param name="voltage">待验证的电压值，单位：伏特(V)</param>
        /// <returns>电压值有效返回true，无效返回false</returns>
        /// <remarks>
        /// 验证电压值是否在设备支持的范围内（0-150V）
        /// 如果电压值超出范围，会记录警告日志并返回false
        ///
        /// 验证规则：
        /// - 最小值：0.0V
        /// - 最大值：150.0V（根据设备型号可能不同）
        /// - 精度：0.01V
        ///
        /// 建议在调用SetVoltageAsync前先调用此方法验证参数
        /// </remarks>
        private bool ValidateVoltage(double voltage)
        {
            if (voltage < MIN_VOLTAGE || voltage > MAX_VOLTAGE)
            {
                App.AlarmService.Warning("参数验证", $"ITECH电源 {DeviceName} 电压值超出范围: {voltage:F2}V (范围: {MIN_VOLTAGE}-{MAX_VOLTAGE}V)");
                return false;
            }
            return true;
        }

        /// <summary>
        /// 验证电流值是否在有效范围内
        /// </summary>
        /// <param name="current">待验证的电流值，单位：安培(A)</param>
        /// <returns>电流值有效返回true，无效返回false</returns>
        /// <remarks>
        /// 验证电流值是否在设备支持的范围内（0-30A）
        /// 如果电流值超出范围，会记录警告日志并返回false
        ///
        /// 验证规则：
        /// - 最小值：0.0A
        /// - 最大值：30.0A（根据设备型号可能不同）
        /// - 精度：0.001A
        ///
        /// 建议在调用SetCurrentAsync前先调用此方法验证参数
        /// </remarks>
        private bool ValidateCurrent(double current)
        {
            if (current < MIN_CURRENT || current > MAX_CURRENT)
            {
                App.AlarmService.Warning("参数验证", $"ITECH电源 {DeviceName} 电流值超出范围: {current:F3}A (范围: {MIN_CURRENT}-{MAX_CURRENT}A)");
                return false;
            }
            return true;
        }

        /// <summary>
        /// 从设备IDN响应字符串中提取序列号
        /// </summary>
        /// <param name="idnResponse">设备IDN命令的响应字符串</param>
        /// <returns>提取的序列号，提取失败返回"Unknown"</returns>
        /// <remarks>
        /// IDN响应格式通常为：制造商,型号,序列号,固件版本
        /// 此方法提取第三个字段作为序列号
        ///
        /// 示例IDN响应：
        /// "ITECH,IT-M3900D,123456789,V1.0"
        /// 提取结果："123456789"
        ///
        /// 如果响应格式不符合预期或为空，返回"Unknown"
        /// </remarks>
        private static string ExtractSerialNumber(string? idnResponse)
        {
            if (string.IsNullOrEmpty(idnResponse))
                return "Unknown";

            var parts = idnResponse.Split(',');
            return parts.Length >= 3 ? parts[2].Trim() : "Unknown";
        }

        /// <summary>
        /// 触发数据变化事件，通知订阅者设备数据已更新
        /// </summary>
        /// <remarks>
        /// 创建PowerSupplyDataChangedEventArgs事件参数并触发DataChanged事件
        /// 事件参数包含当前的所有关键数据：
        /// - 设备ID
        /// - 当前电压
        /// - 当前电流
        /// - 当前功率
        /// - 输出状态
        /// - 工作模式
        ///
        /// 此方法在以下情况下被调用：
        /// - 设置电压电流后
        /// - 开关输出后
        /// - 切换工作模式后
        ///
        /// 如果事件处理过程中发生异常，会记录警告日志但不会影响主流程
        /// </remarks>
        private void OnDataChanged()
        {
            try
            {
                DataChanged?.Invoke(this, new PowerSupplyDataChangedEventArgs(
                    DeviceId, _currentVoltage, _currentCurrent, _currentPower, _isOutputEnabled, _workingMode));
            }
            catch (Exception ex)
            {
                App.AlarmService.Warning("事件处理", $"ITECH电源 {DeviceName} 数据变化事件处理失败: {ex.Message}");
            }
        }

        #endregion
    }
}
