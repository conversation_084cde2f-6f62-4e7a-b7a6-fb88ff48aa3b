using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System;

namespace PEMTestSystem.Models.Core
{
    /// <summary>
    /// 实验数据点实体类
    /// </summary>
    public class DataPoint
    {
        [Key]
        public long Id { get; set; }

        [Required]
        public Guid ExperimentId { get; set; }

        /// <summary>
        /// 数据采集时间戳，而不是保存到数据库的时刻，从而没有使用DateTime.Now来自动建立时间。
        /// </summary>
        [Column(TypeName = "datetime2(3)")]
        [Range(typeof(DateTime), "1/1/1900", "1/1/2099", ErrorMessage = "时间必须在1900-2099之间")] // 验证日期范围的属性，范围为1900-2099年
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// 相对于实验开始时间的秒数
        /// </summary>
        [Column(TypeName = "decimal(18,3)")]
        [Range(0, double.MaxValue, ErrorMessage = "经过时间不能为负数")]
        public decimal ElapsedSeconds { get; set; }

        /// <summary>
        /// 电压，精度0.001V，范围0-10V
        /// </summary>
        [Column(TypeName = "decimal(10,3)")]
        public decimal Voltage { get; set; }

        /// <summary>
        /// 电流，精度0.01A，范围0-170A
        /// </summary>
        [Column(TypeName = "decimal(10,2)")]
        public decimal Current { get; set; }

        /// <summary>
        /// 温度，精度0.1°C
        /// </summary>
        [Column(TypeName = "decimal(5,1)")]
        public decimal Temperature { get; set; }

        /// <summary>
        /// 流量1，精度0.1mL/min
        /// </summary>
        [Column(TypeName = "decimal(8,1)")]
        public decimal FlowRate1 { get; set; }

        /// <summary>
        /// 流量2，精度0.1mL/min
        /// </summary>
        [Column(TypeName = "decimal(8,1)")]
        public decimal FlowRate2 { get; set; }

        /// <summary>
        /// 数据质量标识
        /// </summary>
        [Required]
        public DataQuality Quality { get; set; } = DataQuality.Normal;

        /// <summary>
        /// 备注信息
        /// </summary>
        [MaxLength(500)]
        public string? Notes { get; set; }

        // 导航属性
        [ForeignKey(nameof(ExperimentId))]
        public virtual Experiment Experiment { get; set; } = null!;
    }

    public enum DataQuality
    {
        Normal = 1,     // 正常
        Suspicious = 2, // 可疑
        Error = 3       // 错误
    }
}
