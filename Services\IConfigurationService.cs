namespace PEMTestSystem.Services
{
    /// <summary>
    /// 配置服务接口
    /// </summary>
    public interface IConfigurationService
    {
        string GetStringSetting(string key, string defaultValue = "");
        bool GetBooleanSetting(string key, bool defaultValue = false);
        double GetDoubleSetting(string key, double defaultValue = 0.0);
        int GetIntSetting(string key, int defaultValue = 0);
        T GetSetting<T>(string key, T defaultValue = default!);
        void SetSetting(string key, object value);
    }
}
