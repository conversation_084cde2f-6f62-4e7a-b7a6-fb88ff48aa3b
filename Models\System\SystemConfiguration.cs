using System;
using System.ComponentModel.DataAnnotations;

namespace PEMTestSystem.Models.System
{
    /// <summary>
    /// 系统配置实体类
    /// </summary>
    public class SystemConfiguration
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [MaxLength(100)]
        public string ConfigurationKey { get; set; } = string.Empty;

        public string? ConfigurationValue { get; set; }

        [MaxLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// 配置分类：Database, Device, Safety, UI
        /// </summary>
        [Required]
        [MaxLength(50)]
        public string Category { get; set; } = string.Empty;

        /// <summary>
        /// 数据类型：String, Integer, Decimal, Boolean, Json
        /// </summary>
        [Required]
        [MaxLength(20)]
        public string DataType { get; set; } = string.Empty;

        /// <summary>
        /// 是否加密存储
        /// </summary>
        public bool IsEncrypted { get; set; } = false;

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime UpdatedAt { get; set; } = DateTime.Now;
    }
}