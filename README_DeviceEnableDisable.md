# PEM电解槽自动化测试系统 - 设备禁用/启用功能

## 功能概述

本功能允许用户在运行时动态禁用或启用系统中的设备，提供了灵活的设备管理能力。当设备出现故障或需要维护时，可以单独禁用该设备而不影响整个系统的运行。

## 核心功能

### 1. 设备禁用功能
- **立即停止自动化操作**：禁用设备后，系统立即停止对该设备的所有自动化操作
- **停止数据采集**：不再从该设备读取传感器数据、状态信息
- **停止控制指令**：不再向该设备发送任何自动控制命令
- **停止报警监控**：该设备相关的所有报警检查和通知
- **优雅断开通讯**：安全地断开与设备的Modbus/TCP/串口等通讯连接

### 2. 设备启用功能
- **恢复自动化操作**：启用设备后，恢复该设备的所有正常自动化操作
- **重新建立通讯连接**：自动重新连接设备
- **恢复数据采集**：重新开始从设备采集数据
- **恢复控制指令**：恢复向设备发送控制命令
- **恢复报警监控**：重新启动设备相关的报警监控

### 3. 用户界面反馈
- **视觉标识**：被禁用的设备在界面上有明显的视觉标识（删除线、灰色显示等）
- **状态显示**：设备状态标签显示"已禁用"状态
- **实时更新**：设备状态变化时UI实时更新

## 技术实现

### 1. 数据库层
- **Device模型扩展**：在Device表中添加了`IsEnabled`字段
- **状态持久化**：设备的禁用/启用状态会持久化保存到数据库
- **程序重启恢复**：程序重启后能正确恢复各设备的禁用/启用状态

### 2. 服务层
- **DeviceManager扩展**：
  - `DisableDeviceAsync(deviceId)` - 禁用设备
  - `EnableDeviceAsync(deviceId)` - 启用设备
  - `IsDeviceEnabledAsync(deviceId)` - 检查设备是否启用
  - `GetEnabledDevicesAsync()` - 获取所有已启用的设备

### 3. 数据采集层
- **DataAcquisitionService修改**：在采集数据时自动跳过已禁用的设备
- **智能处理**：禁用设备时使用默认值或零值，不影响数据采集流程

### 4. UI层
- **实时状态更新**：通过事件机制实现设备状态的实时UI更新
- **用户交互**：点击禁用/启用按钮即可切换设备状态
- **状态反馈**：操作成功或失败都有明确的用户反馈

## 使用方法

### 1. 通过UI界面操作
1. 在主窗口的设备状态面板中找到目标设备
2. 点击设备行右侧的"禁用"或"启用"按钮
3. 系统会显示操作结果的消息框
4. 设备状态会实时更新在界面上

### 2. 通过代码调用
```csharp
// 获取设备管理器实例
var deviceManager = serviceProvider.GetRequiredService<DeviceManager>();

// 禁用设备
bool success = await deviceManager.DisableDeviceAsync("TempController_Main");
if (success)
{
    Console.WriteLine("设备已成功禁用");
}

// 启用设备
success = await deviceManager.EnableDeviceAsync("TempController_Main");
if (success)
{
    Console.WriteLine("设备已成功启用");
}

// 检查设备状态
bool isEnabled = await deviceManager.IsDeviceEnabledAsync("TempController_Main");
Console.WriteLine($"设备启用状态: {(isEnabled ? "启用" : "禁用")}");

// 获取所有已启用的设备
var enabledDevices = await deviceManager.GetEnabledDevicesAsync();
Console.WriteLine($"已启用设备数量: {enabledDevices.Count}");
```

## 设备ID映射

系统中的设备名称与设备ID的映射关系：

| 设备名称 | 设备ID |
|---------|--------|
| 直流电源 | PowerSupply_Main |
| 温控仪 | TempController_Main |
| 流量泵 1 | Pump_01 |
| 流量泵 2 | Pump_02 |
| 数据库 | Database_Main |

## 业务价值

1. **提高系统容错性**：单个设备故障不会影响整个系统运行
2. **支持维护操作**：可以在不停止实验的情况下维护单个设备
3. **灵活的运行模式**：支持部分设备离线的运行模式
4. **便于故障排查**：可以逐个排除设备问题

## 安全考虑

1. **关键设备保护**：某些关键设备（如直流电源、数据库）不允许禁用
2. **操作日志记录**：所有禁用/启用操作都会记录到系统日志
3. **状态一致性**：确保UI显示状态与实际设备状态保持一致
4. **异常处理**：完善的异常处理机制，确保操作失败时的系统稳定性

## 测试验证

系统提供了完整的测试套件来验证功能正确性：

```bash
# 运行测试
dotnet run --project Tests/TestRunner.cs
```

测试覆盖以下场景：
- 设备禁用功能测试
- 设备启用功能测试
- 获取已启用设备列表测试
- 状态持久化测试
- UI更新测试

## 注意事项

1. **数据库迁移**：首次使用需要运行数据库迁移来添加IsEnabled字段
2. **设备重连**：启用设备时可能需要一些时间来重新建立连接
3. **实验影响**：禁用关键设备可能会影响正在进行的实验
4. **权限控制**：建议在生产环境中添加适当的权限控制机制

## 故障排除

### 常见问题

1. **设备禁用后无法启用**
   - 检查设备连接是否正常
   - 查看系统日志中的错误信息
   - 确认设备配置是否正确

2. **UI状态显示不正确**
   - 刷新界面或重启程序
   - 检查事件订阅是否正常
   - 查看数据库中的实际状态

3. **数据采集异常**
   - 确认禁用的设备不是关键数据源
   - 检查数据采集服务的日志
   - 验证其他设备的连接状态

### 日志查看

系统会在以下情况记录日志：
- 设备禁用/启用操作
- 状态变更事件
- 通讯连接变化
- 数据采集跳过禁用设备

查看AlarmService的日志输出可以帮助诊断问题。
