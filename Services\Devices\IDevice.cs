using System;
using System.Threading.Tasks;

namespace PEMTestSystem.Services.Devices
{
    /// <summary>
    /// 设备接口定义文件
    /// 包含所有设备类型的接口定义、枚举、事件参数和数据模型
    /// </summary>
    /// <remarks>
    /// 该文件定义了设备驱动系统的核心接口和数据结构，包括：
    /// - 设备基础接口 IDevice
    /// - 温控器接口 ITemperatureController
    /// - 流量泵接口 IFlowPump
    /// - 电源接口 IPowerSupply
    /// - 设备状态枚举和事件参数
    /// - 设备信息数据模型
    ///
    /// ==================== 接口和类型目录索引 ====================
    ///
    /// 【枚举类型】
    /// - DeviceStatus                                         // 设备状态枚举
    /// - PowerSupplyMode                                      // 电源工作模式枚举
    ///
    /// 【核心接口】
    /// - IDevice                                              // 设备基础接口
    ///   * 属性: DeviceId, Type, Status, DeviceName, Model, IsConnected, LastCommunicationTime
    ///   * 事件: StatusChanged
    ///   * 方法: ConnectAsync, DisconnectAsync, IsConnectedAsync, InitializeAsync, ResetAsync, GetDeviceInfoAsync
    ///
    /// - ITemperatureController : IDevice                     // 温控器接口
    ///   * 属性: CurrentTemperature, TargetTemperature, IsHeating
    ///   * 事件: TemperatureChanged
    ///   * 方法: SetTargetTemperatureAsync, GetCurrentTemperatureAsync, GetTargetTemperatureAsync
    ///           StartHeatingAsync, StopHeatingAsync, GetHeatingStatusAsync, SetAlarmTemperatureAsync
    ///
    /// - IFlowPump : IDevice                                  // 流量泵接口
    ///   * 属性: CurrentFlowRate, TargetFlowRate, IsRunning
    ///   * 事件: FlowRateChanged
    ///   * 方法: SetFlowRateAsync, GetFlowRateAsync, StartAsync, StopAsync
    ///           GetRunningStatusAsync, SetDirectionAsync
    ///
    /// - IPowerSupply : IDevice                               // 电源接口
    ///   * 属性: CurrentVoltage, SetVoltage, CurrentCurrent, SetCurrent, CurrentPower
    ///           IsOutputEnabled, WorkingMode
    ///   * 事件: DataChanged
    ///   * 方法: SetVoltageAsync, SetCurrentAsync, SetModeAsync, EnableOutputAsync, DisableOutputAsync
    ///           SetOutputAsync, GetVoltageAsync, GetCurrentAsync, GetPowerAsync, GetOutputStatusAsync
    ///           GetSetVoltageAsync, GetSetCurrentAsync, ClearProtectionAsync, GetErrorAsync
    ///
    /// 【事件参数类】
    /// - DeviceStatusChangedEventArgs                         // 设备状态变化事件参数
    /// - TemperatureChangedEventArgs                          // 温度变化事件参数
    /// - FlowRateChangedEventArgs                             // 流量变化事件参数
    /// - PowerSupplyDataChangedEventArgs                      // 电源数据变化事件参数
    ///
    /// 【数据模型类】
    /// - DeviceInfo                                           // 设备信息数据模型
    ///
    /// ====================================================
    /// </remarks>

    /// <summary>
    /// 设备状态枚举
    /// </summary>
    public enum DeviceStatus
    {
        /// <summary>
        /// 未连接
        /// </summary>
        Disconnected = 0,
        
        /// <summary>
        /// 已连接
        /// </summary>
        Connected = 1,
        
        /// <summary>
        /// 连接中
        /// </summary>
        Connecting = 2,
        
        /// <summary>
        /// 错误状态
        /// </summary>
        Error = 3,
        
        /// <summary>
        /// 运行中
        /// </summary>
        Running = 4,
        
        /// <summary>
        /// 已停止
        /// </summary>
        Stopped = 5
    }

    /// <summary>
    /// 设备基础接口
    /// </summary>
    public interface IDevice : IDisposable
    {
        /// <summary>
        /// 设备ID
        /// </summary>
        string DeviceId { get; }
        
        /// <summary>
        /// 设备类型
        /// </summary>
        Models.Devices.DeviceType Type { get; }
        
        /// <summary>
        /// 设备状态
        /// </summary>
        DeviceStatus Status { get; }
        
        /// <summary>
        /// 设备名称
        /// </summary>
        string DeviceName { get; }
        
        /// <summary>
        /// 设备型号
        /// </summary>
        string Model { get; }
        
        /// <summary>
        /// 是否已连接
        /// </summary>
        bool IsConnected { get; }
        
        /// <summary>
        /// 最后通信时间
        /// </summary>
        DateTime LastCommunicationTime { get; }
        
        /// <summary>
        /// 设备状态变化事件
        /// </summary>
        event EventHandler<DeviceStatusChangedEventArgs> StatusChanged;
        
        /// <summary>
        /// 连接到设备
        /// </summary>
        /// <returns>连接是否成功</returns>
        Task<bool> ConnectAsync();
        
        /// <summary>
        /// 断开设备连接
        /// </summary>
        /// <returns>断开是否成功</returns>
        Task<bool> DisconnectAsync();
        
        /// <summary>
        /// 检查设备连接状态
        /// </summary>
        /// <returns>是否已连接</returns>
        Task<bool> IsConnectedAsync();
        
        /// <summary>
        /// 初始化设备
        /// </summary>
        /// <returns>初始化是否成功</returns>
        Task<bool> InitializeAsync();
        
        /// <summary>
        /// 重置设备
        /// </summary>
        /// <returns>重置是否成功</returns>
        Task<bool> ResetAsync();
        
        /// <summary>
        /// 获取设备信息
        /// </summary>
        /// <returns>设备信息</returns>
        Task<DeviceInfo> GetDeviceInfoAsync();
    }

    /// <summary>
    /// 温控器接口
    /// </summary>
    public interface ITemperatureController : IDevice
    {
        /// <summary>
        /// 当前温度
        /// </summary>
        double CurrentTemperature { get; }
        
        /// <summary>
        /// 目标温度
        /// </summary>
        double TargetTemperature { get; }
        
        /// <summary>
        /// 是否正在加热
        /// </summary>
        bool IsHeating { get; }
        
        /// <summary>
        /// 温度变化事件
        /// </summary>
        event EventHandler<TemperatureChangedEventArgs> TemperatureChanged;
        
        /// <summary>
        /// 设置目标温度
        /// </summary>
        /// <param name="temperature">目标温度（°C）</param>
        /// <returns>设置是否成功</returns>
        Task<bool> SetTargetTemperatureAsync(double temperature);
        
        /// <summary>
        /// 获取当前温度
        /// </summary>
        /// <returns>当前温度（°C）</returns>
        Task<double> GetCurrentTemperatureAsync();
        
        /// <summary>
        /// 获取目标温度
        /// </summary>
        /// <returns>目标温度（°C）</returns>
        Task<double> GetTargetTemperatureAsync();
        
        /// <summary>
        /// 开始加热
        /// </summary>
        /// <returns>操作是否成功</returns>
        Task<bool> StartHeatingAsync();
        
        /// <summary>
        /// 停止加热
        /// </summary>
        /// <returns>操作是否成功</returns>
        Task<bool> StopHeatingAsync();
        
        /// <summary>
        /// 获取加热状态
        /// </summary>
        /// <returns>是否正在加热</returns>
        Task<bool> GetHeatingStatusAsync();
        
        /// <summary>
        /// 设置报警温度
        /// </summary>
        /// <param name="alarmType">报警类型（1或2）</param>
        /// <param name="temperature">报警温度</param>
        /// <returns>设置是否成功</returns>
        Task<bool> SetAlarmTemperatureAsync(int alarmType, double temperature);
    }

    /// <summary>
    /// 流量泵接口
    /// </summary>
    public interface IFlowPump : IDevice
    {
        /// <summary>
        /// 当前流量
        /// </summary>
        double CurrentFlowRate { get; }
        
        /// <summary>
        /// 目标流量
        /// </summary>
        double TargetFlowRate { get; }
        
        /// <summary>
        /// 是否正在运行
        /// </summary>
        bool IsRunning { get; }
        
        /// <summary>
        /// 流量变化事件
        /// </summary>
        event EventHandler<FlowRateChangedEventArgs> FlowRateChanged;
        
        /// <summary>
        /// 设置流量
        /// </summary>
        /// <param name="flowRate">目标流量（L/min）</param>
        /// <returns>设置是否成功</returns>
        Task<bool> SetFlowRateAsync(double flowRate);
        
        /// <summary>
        /// 获取当前流量
        /// </summary>
        /// <returns>当前流量（L/min）</returns>
        Task<double> GetFlowRateAsync();
        
        /// <summary>
        /// 启动泵
        /// </summary>
        /// <returns>启动是否成功</returns>
        Task<bool> StartAsync();
        
        /// <summary>
        /// 停止泵
        /// </summary>
        /// <returns>停止是否成功</returns>
        Task<bool> StopAsync();
        
        /// <summary>
        /// 获取运行状态
        /// </summary>
        /// <returns>是否正在运行</returns>
        Task<bool> GetRunningStatusAsync();
        
        /// <summary>
        /// 设置泵方向
        /// </summary>
        /// <param name="reverse">是否反向</param>
        /// <returns>设置是否成功</returns>
        Task<bool> SetDirectionAsync(bool reverse);
    }

    /// <summary>
    /// 电源接口
    /// </summary>
    public interface IPowerSupply : IDevice
    {
        /// <summary>
        /// 当前输出电压
        /// </summary>
        double CurrentVoltage { get; }

        /// <summary>
        /// 设定电压
        /// </summary>
        double SetVoltage { get; }

        /// <summary>
        /// 当前输出电流
        /// </summary>
        double CurrentCurrent { get; }

        /// <summary>
        /// 设定电流
        /// </summary>
        double SetCurrent { get; }

        /// <summary>
        /// 当前输出功率
        /// </summary>
        double CurrentPower { get; }

        /// <summary>
        /// 输出是否开启
        /// </summary>
        bool IsOutputEnabled { get; }

        /// <summary>
        /// 工作模式（恒压CV/恒流CC）
        /// </summary>
        PowerSupplyMode WorkingMode { get; }

        /// <summary>
        /// 电源参数变化事件
        /// </summary>
        event EventHandler<PowerSupplyDataChangedEventArgs> DataChanged;

        /// <summary>
        /// 设置输出电压
        /// </summary>
        /// <param name="voltage">电压值（V）</param>
        /// <returns>设置是否成功</returns>
        Task<bool> SetVoltageAsync(double voltage);

        /// <summary>
        /// 设置输出电流
        /// </summary>
        /// <param name="current">电流值（A）</param>
        /// <returns>设置是否成功</returns>
        Task<bool> SetCurrentAsync(double current);

        /// <summary>
        /// 设置工作模式
        /// </summary>
        /// <param name="mode">工作模式</param>
        /// <returns>设置是否成功</returns>
        Task<bool> SetModeAsync(PowerSupplyMode mode);

        /// <summary>
        /// 开启输出
        /// </summary>
        /// <returns>操作是否成功</returns>
        Task<bool> EnableOutputAsync();

        /// <summary>
        /// 关闭输出
        /// </summary>
        /// <returns>操作是否成功</returns>
        Task<bool> DisableOutputAsync();

        /// <summary>
        /// 设置输出状态
        /// </summary>
        /// <param name="enabled">是否开启输出</param>
        /// <returns>操作是否成功</returns>
        Task<bool> SetOutputAsync(bool enabled);

        /// <summary>
        /// 获取实时电压
        /// </summary>
        /// <returns>当前电压（V）</returns>
        Task<double> GetVoltageAsync();

        /// <summary>
        /// 获取实时电流
        /// </summary>
        /// <returns>当前电流（A）</returns>
        Task<double> GetCurrentAsync();

        /// <summary>
        /// 获取实时功率
        /// </summary>
        /// <returns>当前功率（W）</returns>
        Task<double> GetPowerAsync();

        /// <summary>
        /// 获取输出状态
        /// </summary>
        /// <returns>输出是否开启</returns>
        Task<bool> GetOutputStatusAsync();

        /// <summary>
        /// 获取设定电压
        /// </summary>
        /// <returns>设定电压（V）</returns>
        Task<double> GetSetVoltageAsync();

        /// <summary>
        /// 获取设定电流
        /// </summary>
        /// <returns>设定电流（A）</returns>
        Task<double> GetSetCurrentAsync();

        /// <summary>
        /// 清除保护状态
        /// </summary>
        /// <returns>操作是否成功</returns>
        Task<bool> ClearProtectionAsync();

        /// <summary>
        /// 查询错误信息
        /// </summary>
        /// <returns>错误信息，无错误时返回null</returns>
        Task<string?> GetErrorAsync();
    }

    /// <summary>
    /// 电源工作模式
    /// </summary>
    public enum PowerSupplyMode
    {
        /// <summary>
        /// 恒压模式
        /// </summary>
        ConstantVoltage = 0,

        /// <summary>
        /// 恒流模式
        /// </summary>
        ConstantCurrent = 1
    }

    /// <summary>
    /// 设备状态变化事件参数
    /// </summary>
    public class DeviceStatusChangedEventArgs : EventArgs
    {
        public string DeviceId { get; }
        public DeviceStatus OldStatus { get; }
        public DeviceStatus NewStatus { get; }
        public DateTime Timestamp { get; }
        public string? Message { get; }

        public DeviceStatusChangedEventArgs(string deviceId, DeviceStatus oldStatus, DeviceStatus newStatus, string? message = null)
        {
            DeviceId = deviceId;
            OldStatus = oldStatus;
            NewStatus = newStatus;
            Timestamp = DateTime.Now;
            Message = message;
        }
    }

    /// <summary>
    /// 温度变化事件参数
    /// </summary>
    public class TemperatureChangedEventArgs : EventArgs
    {
        public string DeviceId { get; }
        public double CurrentTemperature { get; }
        public double TargetTemperature { get; }
        public DateTime Timestamp { get; }

        public TemperatureChangedEventArgs(string deviceId, double currentTemperature, double targetTemperature)
        {
            DeviceId = deviceId;
            CurrentTemperature = currentTemperature;
            TargetTemperature = targetTemperature;
            Timestamp = DateTime.Now;
        }
    }

    /// <summary>
    /// 流量变化事件参数
    /// </summary>
    public class FlowRateChangedEventArgs : EventArgs
    {
        public string DeviceId { get; }
        public double CurrentFlowRate { get; }
        public double TargetFlowRate { get; }
        public DateTime Timestamp { get; }

        public FlowRateChangedEventArgs(string deviceId, double currentFlowRate, double targetFlowRate)
        {
            DeviceId = deviceId;
            CurrentFlowRate = currentFlowRate;
            TargetFlowRate = targetFlowRate;
            Timestamp = DateTime.Now;
        }
    }

    /// <summary>
    /// 电源数据变化事件参数
    /// </summary>
    public class PowerSupplyDataChangedEventArgs : EventArgs
    {
        public string DeviceId { get; }
        public double Voltage { get; }
        public double Current { get; }
        public double Power { get; }
        public bool IsOutputEnabled { get; }
        public PowerSupplyMode Mode { get; }
        public DateTime Timestamp { get; }

        public PowerSupplyDataChangedEventArgs(string deviceId, double voltage, double current, double power, bool isOutputEnabled, PowerSupplyMode mode)
        {
            DeviceId = deviceId;
            Voltage = voltage;
            Current = current;
            Power = power;
            IsOutputEnabled = isOutputEnabled;
            Mode = mode;
            Timestamp = DateTime.Now;
        }
    }

    /// <summary>
    /// 设备信息
    /// </summary>
    public class DeviceInfo
    {
        public string DeviceId { get; set; } = string.Empty;
        public string DeviceName { get; set; } = string.Empty;
        public string Model { get; set; } = string.Empty;
        public string Version { get; set; } = string.Empty;
        public string SerialNumber { get; set; } = string.Empty;
        public DateTime LastCalibrationDate { get; set; }
        public Dictionary<string, object> Properties { get; set; } = new();
    }
}
