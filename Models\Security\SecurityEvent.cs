using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using PEMTestSystem.Models.Core;
using PEMTestSystem.Models.Devices;

namespace PEMTestSystem.Models.Security
{
    /// <summary>
    /// 安全事件实体类
    /// </summary>
    public class SecurityEvent
    {
        [Key]
        public long Id { get; set; }

        /// <summary>
        /// 事件类型：1-参数超限, 2-设备故障, 3-通讯中断, 4-温度过高, 5-紧急停止
        /// </summary>
        public SecurityEventType EventType { get; set; }

        /// <summary>
        /// 严重程度：1-Info, 2-Warning, 3-Error, 4-Critical
        /// </summary>
        public SecuritySeverity Severity { get; set; }

        /// <summary>
        /// 事件标题
        /// </summary>
        [Required]
        [MaxLength(200)]
        public string EventTitle { get; set; } = string.Empty;

        /// <summary>
        /// 事件描述
        /// </summary>
        [Column(TypeName = "nvarchar(max)")]
        public string EventDescription { get; set; } = string.Empty;

        /// <summary>
        /// 关联实验ID
        /// </summary>
        public Guid? ExperimentId { get; set; }

        /// <summary>
        /// 关联设备ID
        /// </summary>
        public Guid? DeviceId { get; set; }

        /// <summary>
        /// 采取的处理措施
        /// </summary>
        [MaxLength(1000)]
        public string? ActionTaken { get; set; }

        /// <summary>
        /// 是否已解决
        /// </summary>
        public bool IsResolved { get; set; } = false;

        /// <summary>
        /// 解决时间
        /// </summary>
        public DateTime? ResolvedAt { get; set; }

        /// <summary>
        /// 事件发生时的系统状态快照
        /// </summary>
        [Column(TypeName = "nvarchar(max)")]
        public string? SystemStateSnapshot { get; set; }

        public DateTime Timestamp { get; set; } = DateTime.Now;

        // 导航属性
        [ForeignKey(nameof(ExperimentId))]
        public virtual Experiment? Experiment { get; set; }

        [ForeignKey(nameof(DeviceId))]
        public virtual Device? Device { get; set; }
    }

    public enum SecurityEventType
    {
        ParameterExceeded = 1,      // 参数超限
        DeviceFailure = 2,          // 设备故障
        CommunicationLoss = 3,      // 通讯中断
        TemperatureHigh = 4,        // 温度过高
        EmergencyStop = 5           // 紧急停止
    }

    public enum SecuritySeverity
    {
        Info = 1,
        Warning = 2,
        Error = 3,
        Critical = 4
    }
}