using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using PEMTestSystem.Models.Control;
using PEMTestSystem.Models.Devices;
using PEMTestSystem.Models.System;
using PEMTestSystem.Models.Security;

namespace PEMTestSystem.Models.Core
{
    /// <summary>
    /// 实验实体类
    /// </summary>
    public class Experiment
    {
        [Key]
        public Guid Id { get; set; } = Guid.NewGuid();

        [Required]
        [MaxLength(255)]
        public string Name { get; set; } = string.Empty;

        [MaxLength(1000)]
        public string? Description { get; set; }

        /// <summary>
        /// 实验类型：1-恒定电流, 2-恒定电压, 3-线性提升电压
        /// </summary>
        [Required]
        public ExperimentType ExperimentType { get; set; }



        /// <summary>
        /// 实验状态：1-准备中, 2-运行中, 3-已完成, 4-已停止, 5-错误, 6-已暂停
        /// </summary>
        [Required]
        public ExperimentStatus Status { get; set; } = ExperimentStatus.Preparing;

        // 时间记录（基于需求14）
        /// <summary>
        /// T1: 实验启动请求时刻
        /// </summary>
        public DateTime? RequestTime { get; set; }

        /// <summary>
        /// T2: 设备就绪时刻
        /// </summary>
        public DateTime? DeviceReadyTime { get; set; }

        /// <summary>
        /// T3: 实验开始时刻（电源启动）
        /// </summary>
        public DateTime? ExperimentStartTime { get; set; }

        /// <summary>
        /// T4: 实验结束时刻
        /// </summary>
        public DateTime? ExperimentEndTime { get; set; }

        /// <summary>
        /// 实验配置（JSON格式存储所有参数）
        /// </summary>
        [Required]
        [Column(TypeName = "nvarchar(max)")]
        public string Configuration { get; set; } = string.Empty;

        // 统计信息
        public long TotalDataPoints { get; set; } = 0;
        
        /// <summary>
        /// 计划持续时间（秒）
        /// </summary>
        public int? PlannedDuration { get; set; }

        /// <summary>
        /// 实际持续时间（秒）
        /// </summary>
        public int? ActualDuration { get; set; }

        /// <summary>
        /// 预热时间（秒）
        /// </summary>
        public int? PreheatingDuration { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        // 导航属性
        public virtual ICollection<DataPoint> DataPoints { get; set; } = new List<DataPoint>();
        public virtual ICollection<ParameterChange> ParameterChanges { get; set; } = new List<ParameterChange>();
        public virtual ICollection<PEMTestSystem.Models.Devices.DeviceStatus> DeviceStatuses { get; set; } = new List<PEMTestSystem.Models.Devices.DeviceStatus>();
        public virtual ICollection<SystemLog> SystemLogs { get; set; } = new List<SystemLog>();
        public virtual ICollection<SecurityEvent> SecurityEvents { get; set; } = new List<SecurityEvent>();
    }

    public enum ExperimentType
    {
        ConstantCurrent = 1,    // 恒定电流
        ConstantVoltage = 2,    // 恒定电压
        LinearVoltageRamp = 3   // 线性提升电压
    }

    public enum ExperimentStatus
    {
        Preparing = 1,          // 准备中
        Running = 2,            // 运行中
        Completed = 3,          // 已完成
        Stopped = 4,            // 已停止
        Error = 5,              // 错误
        Paused = 6              // 已暂停
    }
}