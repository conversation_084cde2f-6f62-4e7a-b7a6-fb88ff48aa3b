using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using PEMTestSystem.Models.Core;

namespace PEMTestSystem.Models.Devices
{
    /// <summary>
    /// 设备状态实体类
    /// </summary>
    public class DeviceStatus
    {
        [Key]
        public long Id { get; set; }

        [Required]
        public Guid DeviceId { get; set; }

        public Guid? ExperimentId { get; set; }

        /// <summary>
        /// 设备状态：1-离线, 2-在线, 3-运行中, 4-错误 5-禁用
        /// </summary>
        public DeviceStatusType Status { get; set; }

        /// <summary>
        /// 状态详细信息（JSON格式）
        /// </summary>
        [Column(TypeName = "nvarchar(max)")]
        public string? StatusDetails { get; set; }

        /// <summary>
        /// 设备当前参数（JSON格式）
        /// </summary>
        [Column(TypeName = "nvarchar(max)")]
        public string? CurrentParameters { get; set; }

        /// <summary>
        /// 响应时间（毫秒）
        /// </summary>
        public int? ResponseTime { get; set; }

        /// <summary>
        /// 最后成功通讯时间
        /// </summary>
        public DateTime? LastSuccessfulCommunication { get; set; }

        /// <summary>
        /// 记录时间戳
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;

        // 导航属性
        [ForeignKey(nameof(DeviceId))]
        public virtual Device Device { get; set; } = null!;

        [ForeignKey(nameof(ExperimentId))]
        public virtual Experiment? Experiment { get; set; }
    }

    public enum DeviceStatusType
    {
        Offline = 1,            // 离线
        Online = 2,             // 在线
        Running = 3,            // 运行中
        Error = 4,               // 错误
        Disabled = 5            // 禁用
    }
}