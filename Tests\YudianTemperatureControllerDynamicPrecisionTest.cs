using System;
using System.Threading.Tasks;
using PEMTestSystem.Services.Devices;

namespace PEMTestSystem.Tests
{
    /// <summary>
    /// 宇电温控器动态精度功能测试
    /// 测试LED状态寄存器解析和动态小数位数处理功能
    /// </summary>
    public class YudianTemperatureControllerDynamicPrecisionTest
    {
        /// <summary>
        /// 测试动态温度值处理功能
        /// </summary>
        public static async Task TestDynamicTemperaturePrecision()
        {
            Console.WriteLine("=== 宇电温控器动态精度功能测试 ===");
            
            try
            {
                // 创建温控器驱动实例（使用测试参数）
                var controller = new YudianTemperatureControllerDriver(
                    "COM3",     // 串口号
                    9600,       // 波特率
                    3,          // 从站地址
                    "TC001",    // 设备ID
                    "测试温控器" // 设备名称
                );

                Console.WriteLine("1. 连接温控器...");
                var connected = await controller.ConnectAsync();
                if (!connected)
                {
                    Console.WriteLine("❌ 连接失败");
                    return;
                }
                Console.WriteLine("✅ 连接成功");

                Console.WriteLine("\n2. 测试LED状态解析...");
                var ledStatus = await controller.GetLedStatusAsync();
                Console.WriteLine($"LED状态详情:");
                foreach (var kvp in ledStatus)
                {
                    Console.WriteLine($"  {kvp.Key}: {kvp.Value}");
                }

                Console.WriteLine($"\n3. 当前小数位数: {controller.CurrentDecimalPlaces}");

                Console.WriteLine("\n4. 测试温度读取（动态精度）...");
                var currentTemp = await controller.GetCurrentTemperatureAsync();
                var targetTemp = await controller.GetTargetTemperatureAsync();
                
                Console.WriteLine($"当前温度: {currentTemp.ToString($"F{controller.CurrentDecimalPlaces}")}°C");
                Console.WriteLine($"目标温度: {targetTemp.ToString($"F{controller.CurrentDecimalPlaces}")}°C");

                Console.WriteLine("\n5. 测试温度设置（动态精度）...");
                var testTemperature = 25.5;
                var setResult = await controller.SetTargetTemperatureAsync(testTemperature);
                if (setResult)
                {
                    Console.WriteLine($"✅ 目标温度设置成功: {testTemperature.ToString($"F{controller.CurrentDecimalPlaces}")}°C");
                }
                else
                {
                    Console.WriteLine("❌ 目标温度设置失败");
                }

                Console.WriteLine("\n6. 测试设备信息获取...");
                var deviceInfo = await controller.GetDeviceInfoAsync();
                Console.WriteLine($"设备信息:");
                Console.WriteLine($"  设备ID: {deviceInfo.DeviceId}");
                Console.WriteLine($"  设备名称: {deviceInfo.DeviceName}");
                Console.WriteLine($"  型号: {deviceInfo.Model}");
                Console.WriteLine($"  当前小数位数: {deviceInfo.Properties["CurrentDecimalPlaces"]}");
                Console.WriteLine($"  LED状态: {deviceInfo.Properties["LEDStatus"]}");
                Console.WriteLine($"  温度单位: {deviceInfo.Properties["TemperatureUnit"]}");

                Console.WriteLine("\n7. 测试加热状态...");
                var isHeating = await controller.GetHeatingStatusAsync();
                Console.WriteLine($"加热状态: {(isHeating ? "加热中" : "停止")}");

                Console.WriteLine("\n8. 断开连接...");
                await controller.DisconnectAsync();
                Console.WriteLine("✅ 断开连接成功");

                Console.WriteLine("\n=== 测试完成 ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试异常: {ex.Message}");
                Console.WriteLine($"详细信息: {ex}");
            }
        }

        /// <summary>
        /// 测试LED状态位解析功能
        /// </summary>
        public static void TestLedStatusParsing()
        {
            Console.WriteLine("\n=== LED状态位解析测试 ===");

            // 测试不同的LED状态值
            var testCases = new[]
            {
                new { Value = (ushort)0x0000, Description = "无小数点，所有状态关闭" },
                new { Value = (ushort)0x0100, Description = "1位小数点" },
                new { Value = (ushort)0x0200, Description = "2位小数点" },
                new { Value = (ushort)0x0300, Description = "3位小数点" },
                new { Value = (ushort)0x0101, Description = "1位小数点 + OUT1输出" },
                new { Value = (ushort)0x0120, Description = "1位小数点 + OUT1输出 + 关闭加热" },
                new { Value = (ushort)0x0148, Description = "1位小数点 + OUT1输出 + 华氏度 + AL1报警" }
            };

            foreach (var testCase in testCases)
            {
                Console.WriteLine($"\n测试值: 0x{testCase.Value:X4} ({testCase.Description})");
                
                var decimalBits = (testCase.Value & 0x0300) >> 8;
                var decimalPlaces = decimalBits switch
                {
                    0b00 => 0,
                    0b01 => 1,
                    0b10 => 2,
                    0b11 => 3,
                    _ => 1
                };

                Console.WriteLine($"  小数位数: {decimalPlaces}");
                Console.WriteLine($"  OUT1输出: {(testCase.Value & 0x0001) != 0}");
                Console.WriteLine($"  OUT2输出: {(testCase.Value & 0x0002) != 0}");
                Console.WriteLine($"  自整定: {(testCase.Value & 0x0004) != 0}");
                Console.WriteLine($"  AL1报警: {(testCase.Value & 0x0008) != 0}");
                Console.WriteLine($"  AL2报警: {(testCase.Value & 0x0010) != 0}");
                Console.WriteLine($"  加热状态: {((testCase.Value & 0x0020) == 0 ? "运行" : "停止")}");
                Console.WriteLine($"  温度单位: {((testCase.Value & 0x0040) != 0 ? "华氏度" : "摄氏度")}");
                Console.WriteLine($"  LBAT报警: {(testCase.Value & 0x0080) != 0}");
            }
        }

        /// <summary>
        /// 主测试入口
        /// </summary>
        public static async Task Main(string[] args)
        {
            Console.WriteLine("宇电温控器动态精度功能测试程序");
            Console.WriteLine("=====================================");

            // 测试LED状态位解析
            TestLedStatusParsing();

            // 如果有可用的硬件设备，可以取消注释下面的行进行实际硬件测试
            // await TestDynamicTemperaturePrecision();

            Console.WriteLine("\n按任意键退出...");
            Console.ReadKey();
        }
    }
}
