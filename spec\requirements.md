# 需求文档

## 介绍

PEM电解槽实验测试系统是一个用于控制和监测电解槽实验的综合性软件系统。该系统需要支持三种不同的实验模式，实现高精度的数据采集、实时监控、设备控制以及长期数据存储功能。系统将通过计算机控制电源、水泵、温控器等设备，并采集各设备的运行数据。

## 需求

### 需求 1 - 恒定电流实验模式

**用户故事：** 作为实验人员，我希望能够设置恒定电流模式进行实验，以便观察电压随时间的变化规律。

#### 验收标准

1. 当用户选择恒定电流模式时，系统应当允许设置目标电流值
2. 当实验开始时，系统应当保持电流恒定并记录电压随时间的变化
3. 当实验进行时，系统应当显示电压（纵轴/V）-时间（横轴/s）实时曲线
4. 当达到设定时间时，系统应当自动停止实验
5. 当实验过程中，系统应当以设备精度显示电流和电压数值

### 需求 2 - 恒定电压实验模式

**用户故事：** 作为实验人员，我希望能够设置恒定电压模式进行实验，以便观察电流随时间的变化规律。

#### 验收标准

1. 当用户选择恒定电压模式时，系统应当允许设置目标电压值
2. 当实验开始时，系统应当保持电压恒定并记录电流随时间的变化
3. 当实验进行时，系统应当显示电流（纵轴/A）-时间（横轴/s）实时曲线
4. 当达到设定时间时，系统应当自动停止实验
5. 当实验过程中，系统应当以设备精度显示电流和电压数值

### 需求 3 - 线性提升电压实验模式

**用户故事：** 作为实验人员，我希望能够设置线性提升电压模式进行实验，以便观察电流与电压的关系。

#### 验收标准

1. 当用户选择线性提升电压模式时，系统应当允许设置起始电压、终点电压
2. 当用户配置参数时，系统应当允许设置变化时间或变化斜率
3. 当实验开始时，系统应当按设定斜率线性提升电压
4. 当实验进行时，系统应当显示电流（纵轴/A）-电压（横轴/V）实时曲线
5. 当达到设定的终点电压时，系统应当自动停止实验

### 需求 4 - 高精度数据显示

**用户故事：** 作为实验人员，我希望系统能够以高精度显示测量数据，以确保实验结果的准确性。

#### 验收标准

1. 当系统显示电压数值时，精度应当达到0.001V
2. 当系统显示电流数值时，精度应当达到0.01A
3. 当系统设置电压参数时，输入精度应当达到0.001V
4. 当系统设置电流参数时，输入精度应当达到0.01A

### 需求 5 - 安全保护措施

**用户故事：** 作为实验人员，我希望系统具备安全保护功能，以防止设备损坏和实验事故。

#### 验收标准

1. 当用户设置实验参数时，系统应当限制电压在0-10V范围内，电流在0-170A范围内
2. 当检测到异常情况时（如设备通讯中断、参数超限、温度过高等），系统应当在1秒内停止实验并发出警告
3. 当用户点击紧急停止按钮时，系统应当在500ms内立即切断电源并停止所有设备
4. 当温度超过95°C时，系统应当自动停止实验并发出高温警告
5. 当设备通讯中断超过5秒时，系统应当自动停止实验并发出通讯故障警告

### 需求 6 - 实时数据保存

**用户故事：** 作为实验人员，我希望系统能够实时保存实验数据，以防止长期实验中的数据丢失。

#### 验收标准

1. 当实验开始时，系统应当立即开始数据采集和保存
2. 当采集到新数据时，系统应当立即将数据保存到文件
3. 当实验进行数周或数月时，系统应当确保数据不会因意外情况而丢失
4. 当系统保存数据时，应当包含时间戳、电压、电流等所有关键参数

### 需求 7 - 数据导出功能

**用户故事：** 作为实验人员，我希望能够导出实验数据进行后续分析，并且时间数据应当以秒为单位。

#### 验收标准

1. 当用户选择导出数据时，系统应当允许选择特定时间范围的数据
2. 当导出数据时，时间应当以秒为单位显示，而不仅仅是年月日时分秒格式
3. 当导出完成时，数据应当包含所有测量参数和设备状态信息
4. 当导出数据时，系统应当支持常见的数据格式（如CSV、Excel等）

### 需求 8 - 循环实验控制

**用户故事：** 作为实验人员，我希望能够设置实验的循环次数，以便进行重复性实验。

#### 验收标准

1. 当用户设置实验参数时，系统应当允许设置循环次数
2. 当一个循环完成时，系统应当自动开始下一个循环
3. 当所有循环完成时，系统应当停止实验并保存完整数据
4. 当循环实验进行时，系统应当显示当前循环进度

### 需求 9 - 实验控制功能

**用户故事：** 作为实验人员，我希望能够方便地启动和停止实验，以便灵活控制实验过程。

#### 验收标准

1. 当用户点击开始按钮时，开始实验的流程（如需求13中所描述的实验开启流程）
2. 当用户点击停止按钮时，系统应当立即停止实验并保存数据
3. 当实验运行时，开始按钮应当变为不可用状态
4. 当实验停止时，停止按钮应当变为不可用状态

### 需求 10 - 多设备通讯控制

**用户故事：** 作为实验人员，我希望系统能够与所有实验设备进行通讯，以便统一控制和监测。

#### 验收标准

1. 当系统启动时，应当建立与电源设备的通讯连接，使用以太网通讯，使用电源设备的专有通讯协议。
2. 当系统启动时，应当建立与两个水泵的通讯连接，使用modbus RTU ，RS-485电气接口。
3. 当系统启动时，应当建立与温控器的通讯连接，使用modbus RTU 通讯。
4. 当设备连接成功时，系统应当能够读取设备状态和参数
5. 当需要控制设备时，系统应当能够发送控制指令

### 需求 11 - 设备状态监测

**用户故事：** 作为实验人员，我希望能够实时监测所有设备的运行状态，以便及时发现问题。

#### 验收标准

1. 当实验进行时，系统应当实时显示两个水泵的流量（mL/min）
2. 当实验进行时，系统应当实时显示温度数值
3. 当实验进行时，系统应当实时显示各设备的运行状态
4. 当设备状态异常时，系统应当发出警告提示
5. 当采集设备数据时，系统应当将设备状态数据一并保存

### 需求 12 - 手动设备参数调整

**用户故事：** 作为实验人员，我希望能够手动调整温控器和流量泵的设定值，以便灵活控制实验条件。

#### 验收标准

1. 当用户需要调整时，系统应当允许手动设置温控器的目标温度值
2. 当用户需要调整时，系统应当允许手动设置两个流量泵的目标流量值
3. 当参数调整后，系统应当立即向对应设备发送新的设定值
4. 当设定值更改时，系统应当显示新的目标值和当前实际值
5. 当参数调整时，系统应当记录调整时间和调整值

### 需求 13 - 实验启动前设备预热和就绪检测

**用户故事：** 作为实验人员，我希望系统在正式开始实验前确保所有设备达到设定状态，以保证实验条件的准确性和一致性。

#### 验收标准

1. 当用户点击开始实验按钮时，系统应当首先启动温控器和水泵，而不是立即启动电源
2. 当温控器启动后，系统应当持续监测当前温度直到达到设定温度的±1°C范围内
3. 当水泵启动后，系统应当持续监测当前流量直到达到设定流量的±5%范围内
4. 当温度和流量均达到设定值时，系统应当显示"设备就绪"状态并自动启动电源开始正式实验
5. 当等待设备就绪期间，系统应当实时显示当前温度、流量值与目标值的对比
6. 当设备预热过程中，系统应当显示"准备中"状态，并允许用户取消实验启动流程
7. 当预热时间超过预设的最大等待时间（如30分钟）时，系统应当提示用户检查设备状态
8. 当任一设备无法达到设定值时，系统应当显示具体的错误信息并停止实验启动流程

### 需求 14 - 实验时间精确计算和多时间点记录

**用户故事：** 作为实验人员，我希望系统能够精确区分和记录实验的不同阶段时间，便于后续分析实验效率和设备性能。

#### 验收标准

1. 当用户点击开始实验按钮时，系统应当记录"实验启动请求时刻"（T1）
2. 当温度和流量均达到设定值时，系统应当记录"设备就绪时刻"（T2）
3. 当电源正式启动时，系统应当记录"实验开始时刻"（T3）并开始计算实验时间
4. 当实验结束时，系统应当记录"实验结束时刻"（T4）
5. 当实验进行时，系统显示的实验时间应当从T3开始计算，不包括设备预热时间
6. 当实验完成后，用户应当能够查看完整的时间记录：预热时间（T2-T1）、实验时间（T4-T3）、总耗时（T4-T1）
7. 当保存实验数据时，系统应当将所有时间节点保存到数据库中
8. 当导出数据时，时间信息应当包含相对于实验开始时刻（T3）的秒数，以及绝对时间戳

### 需求 15 - 系统崩溃恢复和实验连续性

**用户故事：** 作为实验人员，我希望系统在崩溃后能够自动恢复并继续实验，以最大限度地减少数据丢失和实验中断。

#### 验收标准

1. 当系统运行时，应当持续保存实验状态信息到持久化存储
2. 当系统崩溃重启后，应当自动检测是否有未完成的实验
3. 当检测到未完成实验时，系统应当自动提示用户是否继续之前的实验
4. 当用户选择继续时，系统应当自动重新连接所有设备并恢复实验状态
5. 当恢复实验时，系统应当从崩溃前的最后保存点继续数据记录
6. 当恢复过程中，系统应当验证设备连接状态并显示恢复进度
7. 当无法完全恢复时，系统应当提供详细的恢复状态报告和建议操作
8. 当实验状态保存时，应当包含实验参数、当前进度、设备状态等所有关键信息


### 需求 16 - 用户界面设计和操作安全

**用户故事：** 作为实验人员，我希望系统界面美观易用且符合行业规范，并在重要操作前有确认提示，以避免误操作。

#### 验收标准

1. 当用户使用系统时，界面应当美观、直观且符合实验设备操作的行业规范
2. 当用户进行重要操作时（如开始实验、停止实验、删除数据、退出系统），系统应当显示确认对话框
3. 当用户设置关键参数时，系统应当提供清晰的参数说明和有效范围提示
4. 当用户操作可能导致数据丢失时，系统应当显示明确的警告信息
5. 当界面布局时，应当将相关功能分组显示，操作流程应当符合实验人员的工作习惯
6. 当用户输入参数时，系统应当提供实时验证和错误提示
7. 当系统状态发生变化时，应当通过颜色、图标等视觉元素清晰地反映当前状态
8. 当用户进行危险操作时（如紧急停止），相关按钮应当有明显的视觉区分和二次确认

### 需求 17 - 多线程架构设计

**用户故事：** 作为实验人员，我希望系统采用多线程设计，确保用户界面响应性和数据采集的连续性。

#### 验收标准

1. 当系统启动时，应当在主UI线程之外创建独立的后台线程处理数据采集
2. 当数据采集线程运行时，应当独立处理设备通讯、数据采集和数据保存
3. 当UI线程需要更新时，后台线程应当通过线程安全的方式通知UI更新
4. 当用户界面操作时，应当通过线程安全的方式向后台线程发送控制指令
5. 当后台线程异常时，应当能够重启后台线程而不影响用户界面
6. 当系统关闭时，应当优雅地停止所有后台线程并保存当前状态
7. 当长时间实验运行时，UI应当保持响应性，不会因数据采集而卡顿
8. 当数据采集频率较高时，应当通过合理的线程调度确保系统稳定性

### 需求 18 - 设备参数实时手动调整

**用户故事：** 作为实验人员，我希望在实验过程中能够实时调整温控器和流量泵的参数，以便根据实验情况灵活优化实验条件。

#### 验收标准

1. 当实验运行时，系统应当提供温控器目标温度的手动调整界面
2. 当实验运行时，系统应当提供两个流量泵目标流量的手动调整界面
3. 当用户调整参数时，系统应当立即向对应设备发送新的设定值
4. 当参数调整后，系统应当实时显示新的目标值和当前实际值
5. 当参数调整时，系统应当在数据记录中标记参数变更时间和变更值
6. 当参数调整可能影响实验结果时，系统应当显示警告提示但允许用户继续
7. 当参数调整超出设备安全范围时，系统应当拒绝调整并显示错误信息
8. 当参数调整历史需要查看时，系统应当提供参数变更日志功能


### 需求 19 - 数据采集频率和实时性

**用户故事：** 作为实验人员，我希望系统能够以合适的频率采集数据，确保数据的完整性和实时性。

#### 验收标准

1. 当实验运行时，系统应当以用户设定的采样间隔（0.1-60秒）采集数据
2. 当采集到新数据时，系统应当在100ms内更新界面显示
3. 当采集到新数据时，系统应当在500ms内保存到数据库
4. 当数据采集出现延迟时，系统应当在状态栏显示延迟警告
5. 当连续3次数据采集失败时，系统应当停止实验并发出错误警告
6. 当实验进行时，系统应当显示当前的数据采集频率和延迟状态
7. 当采样间隔小于1秒时，系统应当提供数据缓冲机制以确保界面响应性
8. 当长期实验运行时，系统应当定期检查数据采集性能并记录统计信息

### 需求 20 - 设备连接和通讯参数

**用户故事：** 作为实验人员，我希望系统能够可靠地连接和控制所有实验设备，并提供设备连接状态的详细信息。

#### 验收标准

1. 当系统启动时，应当建立与爱德克斯IT-M3901D电源的以太网连接（IP地址可配置）
2. 当系统启动时，应当建立与两个卡川DI Pump550流量泵的Modbus RTU连接（波特率9600，从站地址1和2）
3. 当系统启动时，应当建立与宇电MK008温控器的Modbus RTU连接（波特率9600，从站地址3）
4. 当设备连接失败时，系统应当显示具体的错误信息和建议解决方案
5. 当设备连接成功时，系统应当显示设备型号、固件版本等详细信息
6. 当设备通讯中断时，系统应当每5秒尝试重新连接，最多重试3次
7. 当所有重连尝试失败时，系统应当停止实验并通知用户检查设备连接
8. 当系统运行时，应当在界面上实时显示各设备的连接状态和通讯质量

### 需求 21 - 界面设计原型和用户沟通

**用户故事：** 作为项目团队，我希望在编写代码之前先设计出项目的界面原型，以便与实验人员沟通确定界面布局和交互方式。

#### 验收标准

1. 当开始编码实现之前，应当创建完整的界面设计原型
2. 当设计界面原型时，应当使用单个HTML文件实现（包含HTML、CSS、JavaScript）
3. 当原型设计时，应当包含主要的功能界面：实验参数设置、实时数据显示、设备控制等
4. 当原型完成时，应当能够演示主要的用户交互流程和界面布局
5. 当与实验人员沟通时，应当使用原型来确认界面设计是否符合实际使用需求
6. 当界面设计完成后，应当撰写一个沟通确认文档，包含前期沟通的汇总、设计的界面说明以及实验流程描述，主要目的是与实验人员确认
7. 当原型获得确认后，才应当开始正式的代码实现工作
8. 当原型需要修改时，应当能够快速调整HTML文件并重新演示
9. 当原型设计时，应当考虑实际的屏幕尺寸和使用环境

### 需求 22 - 设计确认文档 - 系统设计方案

**用户故事：** 作为项目团队，我希望在界面设计完成后撰写系统设计方案文档，以便与实验人员进行最终确认并记录设计决策。

#### 验收标准

1. 当界面原型设计完成后，应当撰写系统设计方案文档
2. 当撰写文档时，应当包含前期沟通的汇总内容
3. 当撰写文档时，应当包含详细的界面设计说明和截图
4. 当撰写文档时，应当包含完整的实验流程描述
5. 当文档完成时，应当能够作为与实验人员确认的正式材料
6. 当实验人员审阅时，文档应当清晰地展示系统的功能和操作方式
7. 当需要修改时，文档应当记录变更历史和变更原因
8. 当文档确认后，应当作为后续开发的重要参考依据