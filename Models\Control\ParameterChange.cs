using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using PEMTestSystem.Models.Core;

namespace PEMTestSystem.Models.Control
{
    /// <summary>
    /// 实验过程中的参数调整记录实体类
    /// </summary>
    public class ParameterChange
    {
        [Key]
        public long Id { get; set; }

        [Required]
        public Guid ExperimentId { get; set; }

        /// <summary>
        /// 参数名称
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string ParameterName { get; set; } = string.Empty;

        /// <summary>
        /// 参数分类：设备类型
        /// </summary>
        [Required]
        [MaxLength(50)]
        public string ParameterCategory { get; set; } = string.Empty;

        /// <summary>
        /// 原始值
        /// </summary>
        [Column(TypeName = "decimal(18,6)")]
        public decimal OldValue { get; set; }

        /// <summary>
        /// 新值
        /// </summary>
        [Column(TypeName = "decimal(18,6)")]
        public decimal NewValue { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        [Required]
        [MaxLength(20)]
        public string Unit { get; set; } = string.Empty;

        /// <summary>
        /// 变更原因
        /// </summary>
        [MaxLength(500)]
        public string? ChangeReason { get; set; }

        /// <summary>
        /// 变更描述
        /// </summary>
        [MaxLength(1000)]
        public string? ChangeDescription { get; set; }

        /// <summary>
        /// 变更时间
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;

        /// <summary>
        /// 相对于实验开始的时间
        /// </summary>
        [Column(TypeName = "decimal(18,3)")]
        public decimal ElapsedSeconds { get; set; }

        /// <summary>
        /// 变更来源：1-手动调整, 2-自动调整, 3-安全保护
        /// </summary>
        public ChangeSource ChangeSource { get; set; } = ChangeSource.Manual;

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // 导航属性
        [ForeignKey(nameof(ExperimentId))]
        public virtual Experiment Experiment { get; set; } = null!;
    }

    public enum ChangeSource
    {
        Manual = 1,             // 手动调整
        Automatic = 2,          // 自动调整
        SafetyProtection = 3    // 安全保护
    }
}