<Window x:Class="PEMTestSystem.DeviceSettingsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:PEMTestSystem"
        mc:Ignorable="d"
        Title="设备参数设置" Height="700" Width="900"
        WindowStartupLocation="CenterOwner"
        Background="#1a1c23"
        Foreground="#c0c2c9"
        FontFamily="Roboto, Microsoft YaHei"
        FontSize="14"
        ResizeMode="CanResize"
        MinWidth="800"
        MinHeight="600">
    <Window.Resources>
        <!-- 继承主界面的颜色定义 -->
        <SolidColorBrush x:Key="BgColor" Color="#1a1c23"/>
        <SolidColorBrush x:Key="PanelColor" Color="#242731"/>
        <SolidColorBrush x:Key="BorderColor" Color="#3a3f51"/>
        <SolidColorBrush x:Key="PrimaryColor" Color="#00aaff"/>
        <SolidColorBrush x:Key="SecondaryColor" Color="#e1e1e1"/>
        <SolidColorBrush x:Key="TextColor" Color="#c0c2c9"/>
        <SolidColorBrush x:Key="SuccessColor" Color="#00ff9b"/>
        <SolidColorBrush x:Key="DangerColor" Color="#ff4d4d"/>
        <SolidColorBrush x:Key="DisabledBgColor" Color="#444444"/>
        <SolidColorBrush x:Key="DisabledFgColor" Color="#888888"/>

        <!-- 基础控件样式 -->
        <Style TargetType="Button" x:Key="BaseButton">
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border" CornerRadius="4" Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Opacity" Value="0.9"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter TargetName="border" Property="Background" Value="{StaticResource DisabledBgColor}"/>
                                <Setter Property="Foreground" Value="{StaticResource DisabledFgColor}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style TargetType="Button" x:Key="PrimaryButton" BasedOn="{StaticResource BaseButton}">
            <Setter Property="Background" Value="{StaticResource PrimaryColor}"/>
            <Setter Property="Foreground" Value="White"/>
        </Style>

        <Style TargetType="Button" x:Key="SuccessButton" BasedOn="{StaticResource BaseButton}">
            <Setter Property="Background" Value="{StaticResource SuccessColor}"/>
            <Setter Property="Foreground" Value="#000"/>
        </Style>

        <Style TargetType="Button" x:Key="DangerButton" BasedOn="{StaticResource BaseButton}">
            <Setter Property="Background" Value="{StaticResource DangerColor}"/>
            <Setter Property="Foreground" Value="White"/>
        </Style>

        <Style TargetType="Button" x:Key="SecondaryButton">
            <Setter Property="Background" Value="{StaticResource BgColor}"/>
            <Setter Property="Foreground" Value="{StaticResource TextColor}"/>
            <Setter Property="BorderBrush" Value="{StaticResource BorderColor}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="8,6"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border" CornerRadius="4" Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource SecondaryColor}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style TargetType="Border" x:Key="CardStyle">
            <Setter Property="Background" Value="{StaticResource PanelColor}"/>
            <Setter Property="BorderBrush" Value="{StaticResource BorderColor}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="20"/>
        </Style>

        <Style TargetType="TextBlock" x:Key="CardTitleStyle">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="{StaticResource SecondaryColor}"/>
        </Style>

        <Style TargetType="TextBlock" x:Key="SectionTitleStyle">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="{StaticResource PrimaryColor}"/>
            <Setter Property="Margin" Value="0,0,0,10"/>
        </Style>

        <Style TargetType="TextBox">
            <Setter Property="Background" Value="{StaticResource BgColor}"/>
            <Setter Property="BorderBrush" Value="{StaticResource BorderColor}"/>
            <Setter Property="Foreground" Value="{StaticResource SecondaryColor}"/>
            <Setter Property="Padding" Value="8,6"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CaretBrush" Value="{StaticResource PrimaryColor}"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border x:Name="border" CornerRadius="4" Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}">
                            <ScrollViewer x:Name="PART_ContentHost"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsKeyboardFocused" Value="True">
                                <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource PrimaryColor}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style TargetType="ComboBox">
            <Setter Property="Background" Value="{StaticResource BgColor}"/>
            <Setter Property="BorderBrush" Value="{StaticResource BorderColor}"/>
            <Setter Property="Foreground" Value="{StaticResource SecondaryColor}"/>
            <Setter Property="Padding" Value="8,6"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ComboBox">
                        <Grid>
                            <Border x:Name="border" CornerRadius="4" Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="20"/>
                                    </Grid.ColumnDefinitions>
                                    <ContentPresenter Grid.Column="0" ContentSource="SelectionBoxItem" Margin="{TemplateBinding Padding}" VerticalAlignment="Center"/>
                                    <Path Grid.Column="1" Data="M0,0 L4,4 L8,0 Z" Fill="{StaticResource TextColor}" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </Grid>
                            </Border>
                            <Popup x:Name="PART_Popup" IsOpen="{Binding IsDropDownOpen, RelativeSource={RelativeSource TemplatedParent}}" Placement="Bottom">
                                <Border Background="{StaticResource PanelColor}" BorderBrush="{StaticResource BorderColor}" BorderThickness="1" CornerRadius="4">
                                    <ScrollViewer MaxHeight="200">
                                        <ItemsPresenter/>
                                    </ScrollViewer>
                                </Border>
                            </Popup>
                        </Grid>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsKeyboardFocused" Value="True">
                                <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource PrimaryColor}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style TargetType="ComboBoxItem">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="{StaticResource TextColor}"/>
            <Setter Property="Padding" Value="8,4"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ComboBoxItem">
                        <Border x:Name="border" Background="{TemplateBinding Background}" Padding="{TemplateBinding Padding}">
                            <ContentPresenter/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" Value="{StaticResource PrimaryColor}"/>
                                <Setter Property="Foreground" Value="White"/>
                            </Trigger>
                            <Trigger Property="IsSelected" Value="True">
                                <Setter TargetName="border" Property="Background" Value="{StaticResource PrimaryColor}"/>
                                <Setter Property="Foreground" Value="White"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style TargetType="CheckBox">
            <Setter Property="Foreground" Value="{StaticResource TextColor}"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="CheckBox">
                        <StackPanel Orientation="Horizontal">
                            <Border x:Name="checkBorder" Width="16" Height="16" Background="{StaticResource BgColor}" BorderBrush="{StaticResource BorderColor}" BorderThickness="1" CornerRadius="2" VerticalAlignment="Center">
                                <Path x:Name="checkPath" Data="M2,6 L6,10 L14,2" Stroke="{StaticResource SuccessColor}" StrokeThickness="2" Visibility="Collapsed"/>
                            </Border>
                            <ContentPresenter Margin="6,0,0,0" VerticalAlignment="Center"/>
                        </StackPanel>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsChecked" Value="True">
                                <Setter TargetName="checkPath" Property="Visibility" Value="Visible"/>
                                <Setter TargetName="checkBorder" Property="Background" Value="{StaticResource BgColor}"/>
                            </Trigger>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="checkBorder" Property="BorderBrush" Value="{StaticResource PrimaryColor}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- TabControl 样式 -->
        <Style TargetType="TabControl" x:Key="MainTabControlStyle">
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TabControl">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>
                            <TabPanel Grid.Row="0" IsItemsHost="True" Background="Transparent" Margin="0,0,0,-1"/>
                            <Border Grid.Row="1" BorderBrush="{StaticResource BorderColor}" BorderThickness="0,1,0,0">
                                <ContentPresenter ContentSource="SelectedContent"/>
                            </Border>
                        </Grid>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        <Style TargetType="TabItem" x:Key="MainTabItemStyle">
            <Setter Property="Foreground" Value="{StaticResource TextColor}"/>
            <Setter Property="Padding" Value="15,10"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TabItem">
                        <Border x:Name="border" BorderBrush="Transparent" BorderThickness="0,0,0,2" Margin="0,0,10,0">
                            <ContentPresenter ContentSource="Header"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsSelected" Value="True">
                                <Setter Property="Foreground" Value="{StaticResource PrimaryColor}"/>
                                <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource PrimaryColor}"/>
                            </Trigger>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Foreground" Value="{StaticResource PrimaryColor}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Border Grid.Row="0" Background="{StaticResource PanelColor}" BorderBrush="{StaticResource BorderColor}" BorderThickness="0,0,0,1">
            <StackPanel Orientation="Horizontal" Margin="20,15">
                <Path Data="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2M21 9V7L15 1H5C3.89 1 3 1.9 3 3V21C3 22.1 3.89 23 5 23H19C20.1 23 21 22.1 21 21V9M19 9H14V4H5V19H19V9Z" 
                      Fill="{StaticResource PrimaryColor}" Width="20" Height="20" Stretch="Uniform"/>
                <TextBlock Text="设备参数设置" VerticalAlignment="Center" FontSize="18" FontWeight="SemiBold" Foreground="{StaticResource SecondaryColor}" Margin="10,0,0,0"/>
            </StackPanel>
        </Border>

        <!-- 主要内容区域 -->
        <TabControl Grid.Row="1" x:Name="DeviceTabControl" 
                    Style="{StaticResource MainTabControlStyle}" 
                    ItemContainerStyle="{StaticResource MainTabItemStyle}"
                    Margin="15">
            
            <!-- 电源设置 -->
            <TabItem Header="电源">
                <ScrollViewer VerticalScrollBarVisibility="Auto" Margin="20">
                    <StackPanel>
                        <TextBlock Text="串口通讯参数" Style="{StaticResource SectionTitleStyle}"/>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="35"/>
                                <RowDefinition Height="35"/>
                                <RowDefinition Height="35"/>
                                <RowDefinition Height="35"/>
                            </Grid.RowDefinitions>
                            
                            <TextBlock Text="串口号:" Grid.Row="0" Grid.Column="0" VerticalAlignment="Center"/>
                            <ComboBox x:Name="PowerPortCombo" Grid.Row="0" Grid.Column="1" Margin="5,0,10,0"/>
                            
                            <TextBlock Text="波特率:" Grid.Row="0" Grid.Column="2" VerticalAlignment="Center"/>
                            <ComboBox x:Name="PowerBaudRateCombo" Grid.Row="0" Grid.Column="3" Margin="5,0,0,0">
                                <ComboBoxItem Content="9600" IsSelected="True"/>
                                <ComboBoxItem Content="19200"/>
                                <ComboBoxItem Content="38400"/>
                                <ComboBoxItem Content="57600"/>
                                <ComboBoxItem Content="115200"/>
                            </ComboBox>
                            
                            <TextBlock Text="数据位:" Grid.Row="1" Grid.Column="0" VerticalAlignment="Center"/>
                            <ComboBox x:Name="PowerDataBitsCombo" Grid.Row="1" Grid.Column="1" Margin="5,0,10,0">
                                <ComboBoxItem Content="7"/>
                                <ComboBoxItem Content="8" IsSelected="True"/>
                            </ComboBox>
                            
                            <TextBlock Text="停止位:" Grid.Row="1" Grid.Column="2" VerticalAlignment="Center"/>
                            <ComboBox x:Name="PowerStopBitsCombo" Grid.Row="1" Grid.Column="3" Margin="5,0,0,0">
                                <ComboBoxItem Content="1" IsSelected="True"/>
                                <ComboBoxItem Content="2"/>
                            </ComboBox>
                            
                            <TextBlock Text="校验位:" Grid.Row="2" Grid.Column="0" VerticalAlignment="Center"/>
                            <ComboBox x:Name="PowerParityCombo" Grid.Row="2" Grid.Column="1" Margin="5,0,10,0">
                                <ComboBoxItem Content="None" IsSelected="True"/>
                                <ComboBoxItem Content="Even"/>
                                <ComboBoxItem Content="Odd"/>
                            </ComboBox>
                            
                            <TextBlock Text="设备地址:" Grid.Row="2" Grid.Column="2" VerticalAlignment="Center"/>
                            <TextBox x:Name="PowerAddressBox" Grid.Row="2" Grid.Column="3" Text="1" Margin="5,0,0,0"/>
                        </Grid>
                        
                        <StackPanel Orientation="Horizontal" Margin="0,20,0,0">
                            <Button x:Name="TestPowerButton" Content="通讯测试" Style="{StaticResource PrimaryButton}" Width="100" Height=" 35" Click="TestPowerButton_Click"/>
                            <TextBlock x:Name="PowerStatusText" Text="未测试" VerticalAlignment="Center" Margin="15,0,0,0" FontWeight="SemiBold"/>
                        </StackPanel>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <!-- 温控仪设置 -->
            <TabItem Header="温控仪">
                <ScrollViewer VerticalScrollBarVisibility="Auto" Margin="20">
                    <StackPanel>
                        <TextBlock Text="Modbus RTU 通讯参数" Style="{StaticResource SectionTitleStyle}"/>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="35"/>
                                <RowDefinition Height="35"/>
                                <RowDefinition Height="35"/>
                                <RowDefinition Height="35"/>
                            </Grid.RowDefinitions>
                            
                            <TextBlock Text="串口号:" Grid.Row="0" Grid.Column="0" VerticalAlignment="Center"/>
                            <ComboBox x:Name="TempControllerPortCombo" Grid.Row="0" Grid.Column="1" Margin="5,0,10,0"/>
                            
                            <TextBlock Text="波特率:" Grid.Row="0" Grid.Column="2" VerticalAlignment="Center"/>
                            <ComboBox x:Name="TempControllerBaudRateCombo" Grid.Row="0" Grid.Column="3" Margin="5,0,0,0">
                                <ComboBoxItem Content="9600" IsSelected="True"/>
                                <ComboBoxItem Content="19200"/>
                                <ComboBoxItem Content="38400"/>
                                <ComboBoxItem Content="57600"/>
                                <ComboBoxItem Content="115200"/>
                            </ComboBox>
                            
                            <TextBlock Text="数据位:" Grid.Row="1" Grid.Column="0" VerticalAlignment="Center"/>
                            <ComboBox x:Name="TempControllerDataBitsCombo" Grid.Row="1" Grid.Column="1" Margin="5,0,10,0">
                                <ComboBoxItem Content="7"/>
                                <ComboBoxItem Content="8" IsSelected="True"/>
                            </ComboBox>
                            
                            <TextBlock Text="停止位:" Grid.Row="1" Grid.Column="2" VerticalAlignment="Center"/>
                            <ComboBox x:Name="TempControllerStopBitsCombo" Grid.Row="1" Grid.Column="3" Margin="5,0,0,0">
                                <ComboBoxItem Content="1" IsSelected="True"/>
                                <ComboBoxItem Content="2"/>
                            </ComboBox>
                            
                            <TextBlock Text="校验位:" Grid.Row="2" Grid.Column="0" VerticalAlignment="Center"/>
                            <ComboBox x:Name="TempControllerParityCombo" Grid.Row="2" Grid.Column="1" Margin="5,0,10,0">
                                <ComboBoxItem Content="None" IsSelected="True"/>
                                <ComboBoxItem Content="Even"/>
                                <ComboBoxItem Content="Odd"/>
                            </ComboBox>
                            
                            <TextBlock Text="设备地址:" Grid.Row="2" Grid.Column="2" VerticalAlignment="Center"/>
                            <TextBox x:Name="TempControllerAddressBox" Grid.Row="2" Grid.Column="3" Text="1" Margin="5,0,0,0"/>
                        </Grid>
                        
                        <StackPanel Orientation="Horizontal" Margin="0,20,0,0">
                            <Button x:Name="TestTempControllerButton" Content="通讯测试" Style="{StaticResource PrimaryButton}" Width="100" Height=" 35" Click="TestTempControllerButton_Click"/>
                            <TextBlock x:Name="TempControllerStatusText" Text="未测试" VerticalAlignment="Center" Margin="15,0,0,0" FontWeight="SemiBold"/>
                        </StackPanel>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <!-- 流量泵1设置 -->
            <TabItem Header="流量泵1">
                <ScrollViewer VerticalScrollBarVisibility="Auto" Margin="20">
                    <StackPanel>
                        <TextBlock Text="Modbus RTU 通讯参数" Style="{StaticResource SectionTitleStyle}"/>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="35"/>
                                <RowDefinition Height="35"/>
                                <RowDefinition Height="35"/>
                                <RowDefinition Height="35"/>
                            </Grid.RowDefinitions>
                            
                            <TextBlock Text="串口号:" Grid.Row="0" Grid.Column="0" VerticalAlignment="Center"/>
                            <ComboBox x:Name="FlowPump1PortCombo" Grid.Row="0" Grid.Column="1" Margin="5,0,10,0"/>
                            
                            <TextBlock Text="波特率:" Grid.Row="0" Grid.Column="2" VerticalAlignment="Center"/>
                            <ComboBox x:Name="FlowPump1BaudRateCombo" Grid.Row="0" Grid.Column="3" Margin="5,0,0,0">
                                <ComboBoxItem Content="9600" IsSelected="True"/>
                                <ComboBoxItem Content="19200"/>
                                <ComboBoxItem Content="38400"/>
                                <ComboBoxItem Content="57600"/>
                                <ComboBoxItem Content="115200"/>
                            </ComboBox>
                            
                            <TextBlock Text="数据位:" Grid.Row="1" Grid.Column="0" VerticalAlignment="Center"/>
                            <ComboBox x:Name="FlowPump1DataBitsCombo" Grid.Row="1" Grid.Column="1" Margin="5,0,10,0">
                                <ComboBoxItem Content="7"/>
                                <ComboBoxItem Content="8" IsSelected="True"/>
                            </ComboBox>
                            
                            <TextBlock Text="停止位:" Grid.Row="1" Grid.Column="2" VerticalAlignment="Center"/>
                            <ComboBox x:Name="FlowPump1StopBitsCombo" Grid.Row="1" Grid.Column="3" Margin="5,0,0,0">
                                <ComboBoxItem Content="1" IsSelected="True"/>
                                <ComboBoxItem Content="2"/>
                            </ComboBox>
                            
                            <TextBlock Text="校验位:" Grid.Row="2" Grid.Column="0" VerticalAlignment="Center"/>
                            <ComboBox x:Name="FlowPump1ParityCombo" Grid.Row="2" Grid.Column="1" Margin="5,0,10,0">
                                <ComboBoxItem Content="None" IsSelected="True"/>
                                <ComboBoxItem Content="Even"/>
                                <ComboBoxItem Content="Odd"/>
                            </ComboBox>
                            
                            <TextBlock Text="设备地址:" Grid.Row="2" Grid.Column="2" VerticalAlignment="Center"/>
                            <TextBox x:Name="FlowPump1AddressBox" Grid.Row="2" Grid.Column="3" Text="1" Margin="5,0,0,0"/>
                        </Grid>
                        
                        <StackPanel Orientation="Horizontal" Margin="0,20,0,0">
                            <Button x:Name="TestFlowPump1Button" Content="通讯测试" Style="{StaticResource PrimaryButton}" Width="100" Height=" 35" Click="TestFlowPump1Button_Click"/>
                            <TextBlock x:Name="FlowPump1StatusText" Text="未测试" VerticalAlignment="Center" Margin="15,0,0,0" FontWeight="SemiBold"/>
                        </StackPanel>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <!-- 流量泵2设置 -->
            <TabItem Header="流量泵2">
                <ScrollViewer VerticalScrollBarVisibility="Auto" Margin="20">
                    <StackPanel>
                        <TextBlock Text="Modbus RTU 通讯参数" Style="{StaticResource SectionTitleStyle}"/>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="35"/>
                                <RowDefinition Height="35"/>
                                <RowDefinition Height="35"/>
                                <RowDefinition Height="35"/>
                            </Grid.RowDefinitions>
                            
                            <TextBlock Text="串口号:" Grid.Row="0" Grid.Column="0" VerticalAlignment="Center"/>
                            <ComboBox x:Name="FlowPump2PortCombo" Grid.Row="0" Grid.Column="1" Margin="5,0,10,0"/>
                            
                            <TextBlock Text="波特率:" Grid.Row="0" Grid.Column="2" VerticalAlignment="Center"/>
                            <ComboBox x:Name="FlowPump2BaudRateCombo" Grid.Row="0" Grid.Column="3" Margin="5,0,0,0">
                                <ComboBoxItem Content="9600" IsSelected="True"/>
                                <ComboBoxItem Content="19200"/>
                                <ComboBoxItem Content="38400"/>
                                <ComboBoxItem Content="57600"/>
                                <ComboBoxItem Content="115200"/>
                            </ComboBox>
                            
                            <TextBlock Text="数据位:" Grid.Row="1" Grid.Column="0" VerticalAlignment="Center"/>
                            <ComboBox x:Name="FlowPump2DataBitsCombo" Grid.Row="1" Grid.Column="1" Margin="5,0,10,0">
                                <ComboBoxItem Content="7"/>
                                <ComboBoxItem Content="8" IsSelected="True"/>
                            </ComboBox>
                            
                            <TextBlock Text="停止位:" Grid.Row="1" Grid.Column="2" VerticalAlignment="Center"/>
                            <ComboBox x:Name="FlowPump2StopBitsCombo" Grid.Row="1" Grid.Column="3" Margin="5,0,0,0">
                                <ComboBoxItem Content="1" IsSelected="True"/>
                                <ComboBoxItem Content="2"/>
                            </ComboBox>
                            
                            <TextBlock Text="校验位:" Grid.Row="2" Grid.Column="0" VerticalAlignment="Center"/>
                            <ComboBox x:Name="FlowPump2ParityCombo" Grid.Row="2" Grid.Column="1" Margin="5,0,10,0">
                                <ComboBoxItem Content="None" IsSelected="True"/>
                                <ComboBoxItem Content="Even"/>
                                <ComboBoxItem Content="Odd"/>
                            </ComboBox>
                            
                            <TextBlock Text="设备地址:" Grid.Row="2" Grid.Column="2" VerticalAlignment="Center"/>
                            <TextBox x:Name="FlowPump2AddressBox" Grid.Row="2" Grid.Column="3" Text="1" Margin="5,0,0,0"/>
                        </Grid>
                        
                        <StackPanel Orientation="Horizontal" Margin="0,20,0,0">
                            <Button x:Name="TestFlowPump2Button" Content="通讯测试" Style="{StaticResource PrimaryButton}" Width="100" Height=" 35" Click="TestFlowPump2Button_Click"/>
                            <TextBlock x:Name="FlowPump2StatusText" Text="未测试" VerticalAlignment="Center" Margin="15,0,0,0" FontWeight="SemiBold"/>
                        </StackPanel>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <!-- 数据库设置 -->
            <TabItem Header="数据库">
                <ScrollViewer VerticalScrollBarVisibility="Auto" Margin="20">
                    <StackPanel>
                        <TextBlock Text="SQL Server 数据库连接参数" Style="{StaticResource SectionTitleStyle}"/>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="35"/>
                                <RowDefinition Height="35"/>
                                <RowDefinition Height="35"/>
                                <RowDefinition Height="35"/>
                                <RowDefinition Height="35"/>
                            </Grid.RowDefinitions>
                            
                            <TextBlock Text="服务器:" Grid.Row="0" Grid.Column="0" VerticalAlignment="Center"/>
                            <TextBox x:Name="DatabaseServerBox" Grid.Row="0" Grid.Column="1" Text="localhost" Margin="5,0,0,0" ToolTip="数据库服务器地址或实例名"/>
                            
                            <TextBlock Text="数据库名称:" Grid.Row="1" Grid.Column="0" VerticalAlignment="Center"/>
                            <TextBox x:Name="DatabaseNameBox" Grid.Row="1" Grid.Column="1" Text="DataSampling" Margin="5,0,0,0" ToolTip="目标数据库名称"/>
                            
                            <TextBlock Text="用户名:" Grid.Row="2" Grid.Column="0" VerticalAlignment="Center"/>
                            <TextBox x:Name="DatabaseUserBox" Grid.Row="2" Grid.Column="1" Text="sa" Margin="5,0,0,0" ToolTip="数据库登录用户名"/>
                            
                            <TextBlock Text="密码:" Grid.Row="3" Grid.Column="0" VerticalAlignment="Center"/>
                            <PasswordBox x:Name="DatabasePasswordBox" Grid.Row="3" Grid.Column="1" Margin="5,0,0,0" ToolTip="数据库登录密码">
                                <PasswordBox.Style>
                                    <Style TargetType="PasswordBox">
                                        <Setter Property="Background" Value="{StaticResource BgColor}"/>
                                        <Setter Property="BorderBrush" Value="{StaticResource BorderColor}"/>
                                        <Setter Property="Foreground" Value="{StaticResource SecondaryColor}"/>
                                        <Setter Property="Padding" Value="8,6"/>
                                        <Setter Property="BorderThickness" Value="1"/>
                                        <Setter Property="CaretBrush" Value="{StaticResource PrimaryColor}"/>
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate TargetType="PasswordBox">
                                                    <Border x:Name="border" CornerRadius="4" Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}">
                                                        <ScrollViewer x:Name="PART_ContentHost"/>
                                                    </Border>
                                                    <ControlTemplate.Triggers>
                                                        <Trigger Property="IsKeyboardFocused" Value="True">
                                                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource PrimaryColor}"/>
                                                        </Trigger>
                                                    </ControlTemplate.Triggers>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Style>
                                </PasswordBox.Style>
                            </PasswordBox>
                            
                            <CheckBox x:Name="UseWindowsAuthCheckBox" Grid.Row="4" Grid.Column="1" Content="使用Windows身份验证" Margin="5,0,0,0" Checked="UseWindowsAuth_Checked" Unchecked="UseWindowsAuth_Unchecked"/>
                        </Grid>
                        
                        <StackPanel Orientation="Horizontal" Margin="0,20,0,0">
                            <Button x:Name="TestDatabaseButton" Content="连接测试" Style="{StaticResource PrimaryButton}" Width="100" Height=" 35" Click="TestDatabaseButton_Click"/>
                            <TextBlock x:Name="DatabaseStatusText" Text="未测试" VerticalAlignment="Center" Margin="15,0,0,0" FontWeight="SemiBold"/>
                        </StackPanel>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>
        </TabControl>

        <!-- 底部按钮栏 -->
        <Border Grid.Row="2" Background="{StaticResource PanelColor}" BorderBrush="{StaticResource BorderColor}" BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="20,15">
                <Button x:Name="SaveButton" Content="保存设置" Style="{StaticResource SuccessButton}" Width="100" Height="35" Margin="0,0,10,0" Click="SaveButton_Click"/>
                <Button x:Name="CancelButton" Content="取消" Style="{StaticResource SecondaryButton}" Width="80" Height="35" Click="CancelButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window> 